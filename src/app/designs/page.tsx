'use client'

import { useState, useMemo } from 'react'
import { Metadata } from 'next'
import Layout from '@/components/layout/Layout'
import Section from '@/components/ui/Section'
import Grid from '@/components/ui/Grid'
import DesignCard from '@/components/ui/DesignCard'
import Button from '@/components/ui/Button'
import { getAllDesigns, getAllThemes, generatePageMeta } from '@/lib/data'
import { Search, Filter } from 'lucide-react'

export default function DesignsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTheme, setSelectedTheme] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  const allDesigns = getAllDesigns()
  const allThemes = getAllThemes()

  const filteredAndSortedDesigns = useMemo(() => {
    let filtered = allDesigns

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(design =>
        design.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        design.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        design.themes.some(theme => theme.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Filter by theme
    if (selectedTheme !== 'all') {
      filtered = filtered.filter(design =>
        design.themes.includes(selectedTheme)
      )
    }

    // Sort designs
    switch (sortBy) {
      case 'newest':
        return filtered.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
      case 'oldest':
        return filtered.sort((a, b) => new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime())
      case 'name':
        return filtered.sort((a, b) => a.name.localeCompare(b.name))
      default:
        return filtered
    }
  }, [allDesigns, searchTerm, selectedTheme, sortBy])

  return (
    <Layout>
      {/* Hero Section */}
      <Section
        title="Design Gallery"
        subtitle="Complete Collection"
        className="bg-gradient-to-br from-background to-surface"
      >
        <div className="max-w-2xl mx-auto text-center">
          <p className="text-xl text-text-secondary leading-relaxed">
            Explore the complete collection of Femmepod designs. Each piece tells a unique story 
            of empowerment, strength, and artistic vision.
          </p>
        </div>
      </Section>

      {/* Filters Section */}
      <Section animate={false} className="py-8 bg-surface border-y border-border">
        <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={20} />
            <input
              type="text"
              placeholder="Search designs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors"
            />
          </div>

          {/* Theme Filter */}
          <div className="flex items-center gap-4">
            <Filter size={20} className="text-text-secondary" />
            <select
              value={selectedTheme}
              onChange={(e) => setSelectedTheme(e.target.value)}
              className="px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors"
            >
              <option value="all">All Themes</option>
              {allThemes.map(theme => (
                <option key={theme} value={theme}>
                  {theme.charAt(0).toUpperCase() + theme.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div className="flex items-center gap-4">
            <span className="text-text-secondary text-sm">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="name">Name A-Z</option>
            </select>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-6 text-center">
          <p className="text-text-secondary">
            Showing {filteredAndSortedDesigns.length} of {allDesigns.length} designs
            {selectedTheme !== 'all' && ` in "${selectedTheme}"`}
            {searchTerm && ` matching "${searchTerm}"`}
          </p>
        </div>
      </Section>

      {/* Designs Grid */}
      <Section animate={false}>
        {filteredAndSortedDesigns.length > 0 ? (
          <Grid columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}>
            {filteredAndSortedDesigns.map((design) => (
              <DesignCard key={design.id} design={design} />
            ))}
          </Grid>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-6">🎨</div>
            <h3 className="font-heading text-2xl font-semibold text-text-primary mb-4">
              No designs found
            </h3>
            <p className="text-text-secondary mb-8">
              Try adjusting your search terms or filters to find what you're looking for.
            </p>
            <Button
              onClick={() => {
                setSearchTerm('')
                setSelectedTheme('all')
                setSortBy('newest')
              }}
              variant="secondary"
            >
              Clear Filters
            </Button>
          </div>
        )}
      </Section>

      {/* CTA Section */}
      <Section
        title="Can't Find What You're Looking For?"
        subtitle="Custom Commissions"
        className="bg-gradient-to-br from-surface to-background"
      >
        <div className="max-w-2xl mx-auto text-center">
          <p className="text-xl text-text-secondary mb-8 leading-relaxed">
            Every design in this gallery started as a vision. Let's bring your unique vision to life 
            with a custom commission that's perfectly tailored to your story.
          </p>
          <Button href="/custom-commissions" variant="primary" size="lg">
            Commission Custom Art
          </Button>
        </div>
      </Section>
    </Layout>
  )
}
