import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import Layout from '@/components/layout/Layout'
import Section from '@/components/ui/Section'
import Grid from '@/components/ui/Grid'
import DesignCard from '@/components/ui/DesignCard'
import WhereToBuy from '@/components/ui/WhereToBuy'
import Button from '@/components/ui/Button'
import StructuredData from '@/components/seo/StructuredData'
import { getDesignBySlug, getRelatedDesigns, generateDesignMeta, getSiteConfig } from '@/lib/data'
import { ArrowLeft, Calendar, Tag } from 'lucide-react'

interface DesignPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: DesignPageProps): Promise<Metadata> {
  const { slug } = await params
  const design = getDesignBySlug(slug)
  
  if (!design) {
    return {
      title: 'Design Not Found',
    }
  }

  return generateDesignMeta(design)
}

export default async function DesignPage({ params }: DesignPageProps) {
  const { slug } = await params
  const design = getDesignBySlug(slug)

  if (!design) {
    notFound()
  }

  const relatedDesigns = getRelatedDesigns(design, 3)
  const siteConfig = getSiteConfig()

  const breadcrumbs = [
    { name: 'Home', url: siteConfig.url },
    { name: 'Designs', url: `${siteConfig.url}/designs` },
    { name: design.name, url: `${siteConfig.url}/designs/${design.slug}` },
  ]

  return (
    <>
      <StructuredData type="imageObject" design={design} />
      <StructuredData type="breadcrumbList" breadcrumbs={breadcrumbs} />

      <Layout>
      {/* Breadcrumb */}
      <Section animate={false} className="py-8">
        <div className="flex items-center gap-2 text-sm">
          <Link href="/" className="text-text-secondary hover:text-accent-primary transition-colors">
            Home
          </Link>
          <span className="text-text-secondary">/</span>
          <Link href="/designs" className="text-text-secondary hover:text-accent-primary transition-colors">
            Designs
          </Link>
          <span className="text-text-secondary">/</span>
          <span className="text-text-primary">{design.name}</span>
        </div>
        
        <Link 
          href="/designs" 
          className="inline-flex items-center gap-2 mt-4 text-text-secondary hover:text-accent-primary transition-colors"
        >
          <ArrowLeft size={16} />
          Back to Gallery
        </Link>
      </Section>

      {/* Design Details */}
      <Section animate={false}>
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Design Image */}
          <div className="relative aspect-square bg-surface border border-border overflow-hidden">
            <Image
              src={design.image}
              alt={design.name}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 1024px) 100vw, 50vw"
            />
            
            {design.featured && (
              <div className="absolute top-6 right-6 bg-accent-primary text-background px-3 py-2 font-medium uppercase tracking-wider">
                Featured Design
              </div>
            )}
          </div>

          {/* Design Info */}
          <div className="space-y-8">
            <div>
              <h1 className="font-heading text-4xl lg:text-5xl font-bold text-text-primary mb-4">
                {design.name}
              </h1>
              
              <p className="text-xl text-text-secondary leading-relaxed mb-6">
                {design.description}
              </p>

              {/* Meta Information */}
              <div className="flex flex-wrap gap-6 text-sm text-text-secondary">
                {design.createdAt && (
                  <div className="flex items-center gap-2">
                    <Calendar size={16} />
                    <span>Created {new Date(design.createdAt).toLocaleDateString()}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Tag size={16} />
                  <span>Available on {design.shopLinks.length} platform{design.shopLinks.length !== 1 ? 's' : ''}</span>
                </div>
              </div>
            </div>

            {/* Themes */}
            <div>
              <h3 className="font-heading text-lg font-semibold text-text-primary mb-3">
                Themes & Style
              </h3>
              <div className="flex flex-wrap gap-2">
                {design.themes.map((theme) => (
                  <Link
                    key={theme}
                    href={`/designs?theme=${theme}`}
                    className="px-3 py-2 bg-background border border-border text-text-secondary hover:border-accent-primary hover:text-accent-primary transition-colors uppercase tracking-wider text-sm"
                  >
                    {theme}
                  </Link>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button href="#where-to-buy" variant="primary" size="lg">
                Shop This Design
              </Button>
              <Button href="/custom-commissions" variant="secondary" size="lg">
                Commission Similar
              </Button>
            </div>
          </div>
        </div>
      </Section>

      {/* Where to Buy Section */}
      <Section id="where-to-buy">
        <WhereToBuy shopLinks={design.shopLinks} designName={design.name} />
      </Section>

      {/* Related Designs */}
      {relatedDesigns.length > 0 && (
        <Section
          title="You Might Also Like"
          subtitle="Related Designs"
          className="bg-surface"
        >
          <Grid columns={{ sm: 1, md: 2, lg: 3 }}>
            {relatedDesigns.map((relatedDesign) => (
              <DesignCard key={relatedDesign.id} design={relatedDesign} />
            ))}
          </Grid>
          
          <div className="text-center mt-12">
            <Button href="/designs" variant="secondary" size="lg">
              View All Designs
            </Button>
          </div>
        </Section>
      )}

      {/* CTA Section */}
      <Section
        title="Love This Style?"
        subtitle="Custom Commissions"
        className="bg-gradient-to-br from-background to-surface"
      >
        <div className="max-w-2xl mx-auto text-center">
          <p className="text-xl text-text-secondary mb-8 leading-relaxed">
            If this design resonates with you, imagine what we could create together. 
            Commission a custom piece that captures your unique vision with the same 
            artistic energy and attention to detail.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button href="/custom-commissions" variant="primary" size="lg">
              Start Your Commission
            </Button>
            <Button href="/community" variant="secondary" size="lg">
              Join the Community
            </Button>
          </div>
        </div>
      </Section>
    </Layout>
    </>
  )
}
