import { Metadata } from 'next'
import Layout from '@/components/layout/Layout'
import Section from '@/components/ui/Section'
import Grid from '@/components/ui/Grid'
import ShopCard from '@/components/ui/ShopCard'
import Button from '@/components/ui/Button'
import { getAllShops, generatePageMeta } from '@/lib/data'
import { Store, Shield, Truck, Heart } from 'lucide-react'

export const metadata: Metadata = {
  ...generatePageMeta('shops'),
}

export default function ShopsPage() {
  const shops = getAllShops()

  return (
    <Layout>
      {/* Hero Section */}
      <Section
        title="Official Femmepod Shops"
        subtitle="Where to Buy"
        className="bg-gradient-to-br from-background to-surface"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-xl text-text-secondary leading-relaxed mb-8">
            Find all official Femmepod merchandise through our trusted partners. 
            Each platform offers unique products and shipping options to serve you better, 
            no matter where you are in the world.
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3">
                <Shield size={24} className="text-background" />
              </div>
              <span className="text-sm font-medium text-text-primary">Quality Guaranteed</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3">
                <Truck size={24} className="text-background" />
              </div>
              <span className="text-sm font-medium text-text-primary">Worldwide Shipping</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3">
                <Store size={24} className="text-background" />
              </div>
              <span className="text-sm font-medium text-text-primary">Multiple Platforms</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3">
                <Heart size={24} className="text-background" />
              </div>
              <span className="text-sm font-medium text-text-primary">Artist Supported</span>
            </div>
          </div>
        </div>
      </Section>

      {/* Shops Grid */}
      <Section
        title="Our Partner Stores"
        subtitle="Choose Your Platform"
      >
        <Grid columns={{ sm: 1, md: 2, lg: 2 }}>
          {shops.map((shop) => (
            <ShopCard key={shop.id} shop={shop} />
          ))}
        </Grid>
      </Section>

      {/* Regional Information */}
      <Section
        title="Regional Availability"
        subtitle="Shipping & Support"
        className="bg-surface"
      >
        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-background border border-border p-6">
            <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
              🇺🇸 United States & International
            </h3>
            <ul className="space-y-2 text-text-secondary">
              <li>• <strong>Redbubble:</strong> Worldwide shipping, extensive product range</li>
              <li>• <strong>Threadless:</strong> Premium quality, US/EU focus</li>
              <li>• <strong>Mini Store:</strong> Curated selection, global shipping</li>
            </ul>
          </div>
          
          <div className="bg-background border border-border p-6">
            <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
              🇮🇳 India & South Asia
            </h3>
            <ul className="space-y-2 text-text-secondary">
              <li>• <strong>Frankly Wearing:</strong> Local production, faster delivery</li>
              <li>• <strong>Redbubble:</strong> International option with local fulfillment</li>
              <li>• <strong>Mini Store:</strong> Global shipping available</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 p-6 bg-accent-primary/10 border border-accent-primary/20 rounded">
          <h4 className="font-heading text-lg font-semibold text-text-primary mb-3">
            💡 Pro Tip: Choose the Right Platform
          </h4>
          <p className="text-text-secondary">
            Each platform offers different products, shipping speeds, and pricing. 
            Redbubble has the widest selection, Threadless focuses on premium apparel, 
            Frankly Wearing is perfect for India, and Mini Store offers curated collections. 
            Check multiple platforms to find the best option for your needs!
          </p>
        </div>
      </Section>

      {/* Quality Guarantee */}
      <Section
        title="Our Quality Promise"
        subtitle="Vector Art Guarantee"
        className="bg-gradient-to-br from-surface to-background"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-xl text-text-secondary mb-8 leading-relaxed">
            Every Femmepod design is created as high-resolution vector art, ensuring 
            crisp, clear prints at any size. Our partners use premium materials and 
            printing techniques to bring your favorite designs to life.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button href="/quality-and-partners" variant="primary" size="lg">
              Learn About Quality
            </Button>
            <Button href="/designs" variant="secondary" size="lg">
              Browse Designs
            </Button>
          </div>
        </div>
      </Section>
    </Layout>
  )
}
