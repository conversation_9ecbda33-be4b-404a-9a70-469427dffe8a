import { Metadata } from 'next'
import Layout from '@/components/layout/Layout'
import Hero from '@/components/ui/Hero'
import Section from '@/components/ui/Section'
import Grid from '@/components/ui/Grid'
import DesignCard from '@/components/ui/DesignCard'
import TestimonialCard from '@/components/ui/TestimonialCard'
import Button from '@/components/ui/Button'
import { getFeaturedDesigns, getRandomTestimonials, getSiteConfig, generatePageMeta } from '@/lib/data'

export const metadata: Metadata = {
  ...generatePageMeta('home'),
}

export default function Home() {
  const featuredDesigns = getFeaturedDesigns()
  const testimonials = getRandomTestimonials(3)
  const siteConfig = getSiteConfig()

  return (
    <Layout>
      {/* Hero Section */}
      <Hero
        title="Art for the Modern Chimera"
        subtitle="Femmepod Portfolio"
        description="Discover powerful, empowering artwork that blends anime aesthetics with feminist themes and futuristic elements. Each design tells a story of strength, rebellion, and beauty."
        primaryCTA={{
          text: "Explore Designs",
          href: "/designs"
        }}
        secondaryCTA={{
          text: "Commission Art",
          href: "/custom-commissions"
        }}
      />

      {/* Featured Designs Section */}
      <Section
        title="Featured Designs"
        subtitle="Latest Creations"
      >
        <Grid columns={{ sm: 1, md: 2, lg: 3 }}>
          {featuredDesigns.map((design, index) => (
            <DesignCard
              key={design.id}
              design={design}
              priority={index < 3}
            />
          ))}
        </Grid>

        <div className="text-center mt-12">
          <Button href="/designs" variant="secondary" size="lg">
            View All Designs
          </Button>
        </div>
      </Section>

      {/* Stats Section */}
      <Section className="bg-surface">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2">
              {siteConfig.stats.customerSatisfaction}
            </div>
            <div className="text-text-secondary uppercase tracking-wider text-sm">
              Customer Satisfaction
            </div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2">
              {siteConfig.stats.fiveStarReviews}
            </div>
            <div className="text-text-secondary uppercase tracking-wider text-sm">
              Five Star Reviews
            </div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2">
              {siteConfig.stats.designsCreated}
            </div>
            <div className="text-text-secondary uppercase tracking-wider text-sm">
              Designs Created
            </div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2">
              {siteConfig.stats.countriesShipped}
            </div>
            <div className="text-text-secondary uppercase tracking-wider text-sm">
              Countries Shipped
            </div>
          </div>
        </div>
      </Section>

      {/* Testimonials Section */}
      <Section
        title="What People Say"
        subtitle="Community Love"
      >
        <Grid columns={{ sm: 1, md: 2, lg: 3 }}>
          {testimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </Grid>
      </Section>

      {/* Custom Work CTA Section */}
      <Section
        title="Ready for Something Unique?"
        subtitle="Custom Commissions"
        className="bg-gradient-to-br from-surface to-background"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-xl text-text-secondary mb-8 leading-relaxed">
            Looking for a custom piece that speaks to your soul? I specialize in creating
            powerful, personalized artwork that captures your vision with the same energy
            and artistry you see in my portfolio.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button href="/custom-commissions" variant="primary" size="lg">
              Start Your Commission
            </Button>
            <Button href="/community" variant="secondary" size="lg">
              Join the Community
            </Button>
          </div>
        </div>
      </Section>
    </Layout>
  )
}
