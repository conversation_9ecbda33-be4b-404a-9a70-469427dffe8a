@import url('https://fonts.googleapis.com/css2?family=Clash+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* ===== FEMMEPOD DESIGN SYSTEM ===== */

:root {
  /* Color Palette - Dark Mode Theme */
  --color-background: #1A1A1A;
  --color-surface: #242424;
  --color-border: #333333;
  --color-text-primary: #F5F5F5;
  --color-text-secondary: #A9A9A9;
  --color-accent-primary: #FF007A;
  --color-accent-secondary: #00F0FF;

  /* Typography System */
  --font-family-heading: 'Clash Display', sans-serif;
  --font-family-body: 'Inter', sans-serif;
  --font-size-h1: clamp(2.5rem, 8vw, 5.5rem);
  --font-size-h2: clamp(2rem, 6vw, 4rem);
  --font-size-body: 1rem;

  /* Layout */
  --max-content-width: 1440px;
  --grid-columns: 12;

  /* Animation */
  --transition-fast: 0.2s ease-out;
  --transition-medium: 0.4s ease-out;
  --transition-slow: 0.6s ease-out;
}

/* ===== GLOBAL STYLES ===== */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-family-body);
  font-size: var(--font-size-body);
  line-height: 1.6;
  overflow-x: hidden;
  cursor: none; /* Custom cursor will be implemented */
}

/* ===== TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: var(--font-size-h1);
  font-weight: 700;
}

h2 {
  font-size: var(--font-size-h2);
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

a {
  color: var(--color-accent-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-accent-secondary);
}

/* ===== BUTTONS ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0;
  font-family: var(--font-family-body);
  font-weight: 500;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--color-accent-primary);
  color: var(--color-background);
}

.btn-primary:hover {
  background: var(--color-accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 122, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--color-accent-primary);
  border: 2px solid var(--color-accent-primary);
}

.btn-secondary:hover {
  background: var(--color-accent-primary);
  color: var(--color-background);
  border-color: var(--color-accent-primary);
}

/* ===== LAYOUT ===== */

.container {
  max-width: var(--max-content-width);
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid {
    gap: 2rem;
  }
}

/* ===== CUSTOM CURSOR ===== */

.cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: var(--color-accent-primary);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform var(--transition-fast);
  mix-blend-mode: difference;
}

.cursor.hover {
  transform: scale(2);
  background: var(--color-accent-secondary);
}

/* ===== ANIMATIONS ===== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* ===== SCROLLBAR STYLING ===== */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-secondary);
}
