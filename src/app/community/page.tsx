import { Metadata } from 'next'
import Layout from '@/components/layout/Layout'
import Section from '@/components/ui/Section'
import Grid from '@/components/ui/Grid'
import TestimonialCard from '@/components/ui/TestimonialCard'
import Button from '@/components/ui/Button'
import { getAllTestimonials, getSiteConfig } from '@/lib/data'
import { Users, Heart, Star, Instagram, MessageCircle, Share2 } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Community | Femmepod',
  description: 'Join the Femmepod community! See testimonials, customer stories, and connect with fellow art lovers who share our passion for empowering design.',
  keywords: ['community', 'testimonials', 'customer stories', 'art community', 'Femmepod fans'],
}

export default function CommunityPage() {
  const testimonials = getAllTestimonials()
  const siteConfig = getSiteConfig()

  return (
    <Layout>
      {/* Hero Section */}
      <Section
        title="Join the Femmepod Community"
        subtitle="Art Lovers Unite"
        className="bg-gradient-to-br from-background to-surface"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-xl text-text-secondary leading-relaxed mb-8">
            Welcome to a community of art lovers, dreamers, and modern chimeras who believe 
            in the power of empowering design. Here's what our amazing community has to say 
            about their Femmepod experience.
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4">
                <Users size={32} className="text-background" />
              </div>
              <div className="text-2xl font-heading font-bold text-text-primary">5K+</div>
              <div className="text-text-secondary text-sm">Community Members</div>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4">
                <Star size={32} className="text-background" />
              </div>
              <div className="text-2xl font-heading font-bold text-text-primary">{siteConfig.stats.fiveStarReviews}</div>
              <div className="text-text-secondary text-sm">5-Star Reviews</div>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4">
                <Heart size={32} className="text-background" />
              </div>
              <div className="text-2xl font-heading font-bold text-text-primary">{siteConfig.stats.customerSatisfaction}</div>
              <div className="text-text-secondary text-sm">Satisfaction Rate</div>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4">
                <Share2 size={32} className="text-background" />
              </div>
              <div className="text-2xl font-heading font-bold text-text-primary">{siteConfig.stats.countriesShipped}</div>
              <div className="text-text-secondary text-sm">Countries Reached</div>
            </div>
          </div>
        </div>
      </Section>

      {/* Community Testimonials */}
      <Section
        title="What Our Community Says"
        subtitle="Real Stories, Real Love"
      >
        <Grid columns={{ sm: 1, md: 2, lg: 3 }}>
          {testimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </Grid>
      </Section>

      {/* User Generated Content */}
      <Section
        title="Community Showcase"
        subtitle="Your Femmepod Style"
        className="bg-surface"
      >
        <div className="text-center mb-12">
          <p className="text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto">
            See how our community styles their Femmepod designs! Tag us on social media 
            to be featured in our community showcase.
          </p>
        </div>
        
        {/* Placeholder for user-generated content grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
            <div key={i} className="aspect-square bg-background border border-border flex items-center justify-center">
              <div className="text-center text-text-secondary">
                <Instagram size={32} className="mx-auto mb-2" />
                <div className="text-sm">Community Photo #{i}</div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-8">
          <p className="text-text-secondary mb-4">
            Share your Femmepod style and get featured!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              href={siteConfig.links.instagram} 
              variant="primary" 
              size="lg"
              external
            >
              <Instagram size={20} className="mr-2" />
              Follow on Instagram
            </Button>
            <Button href="/designs" variant="secondary" size="lg">
              Shop the Collection
            </Button>
          </div>
        </div>
      </Section>

      {/* Community Values */}
      <Section
        title="Our Community Values"
        subtitle="What We Stand For"
      >
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart size={40} className="text-background" />
            </div>
            <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
              Empowerment Through Art
            </h3>
            <p className="text-text-secondary">
              We believe art has the power to inspire, empower, and create positive change. 
              Every design tells a story of strength and resilience.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6">
              <Users size={40} className="text-background" />
            </div>
            <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
              Inclusive Community
            </h3>
            <p className="text-text-secondary">
              Our community welcomes everyone who appreciates powerful, meaningful art. 
              We celebrate diversity and support each other's journeys.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6">
              <Star size={40} className="text-background" />
            </div>
            <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
              Quality & Authenticity
            </h3>
            <p className="text-text-secondary">
              We're committed to creating authentic, high-quality art that resonates 
              with our community and stands the test of time.
            </p>
          </div>
        </div>
      </Section>

      {/* Connect Section */}
      <Section
        title="Connect With Us"
        subtitle="Join the Conversation"
        className="bg-gradient-to-br from-surface to-background"
      >
        <div className="max-w-3xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-background border border-border p-8 text-center">
              <Instagram size={48} className="text-accent-primary mx-auto mb-4" />
              <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
                Follow on Instagram
              </h3>
              <p className="text-text-secondary mb-6">
                Get behind-the-scenes content, work-in-progress shots, and connect 
                with the community daily.
              </p>
              <Button 
                href={siteConfig.links.instagram} 
                variant="primary" 
                external
              >
                @femmepod
              </Button>
            </div>
            
            <div className="bg-background border border-border p-8 text-center">
              <MessageCircle size={48} className="text-accent-primary mx-auto mb-4" />
              <h3 className="font-heading text-xl font-semibold text-text-primary mb-4">
                Get in Touch
              </h3>
              <p className="text-text-secondary mb-6">
                Have questions, feedback, or just want to say hello? 
                We'd love to hear from you!
              </p>
              <Button 
                href={`mailto:${siteConfig.links.email}`} 
                variant="secondary"
              >
                Send Email
              </Button>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <h3 className="font-heading text-2xl font-semibold text-text-primary mb-4">
              Ready to Join the Community?
            </h3>
            <p className="text-xl text-text-secondary mb-8 leading-relaxed">
              Start your journey with a design that speaks to your soul, 
              or commission something uniquely yours.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button href="/designs" variant="primary" size="lg">
                Browse Designs
              </Button>
              <Button href="/custom-commissions" variant="secondary" size="lg">
                Commission Art
              </Button>
            </div>
          </div>
        </div>
      </Section>
    </Layout>
  )
}
