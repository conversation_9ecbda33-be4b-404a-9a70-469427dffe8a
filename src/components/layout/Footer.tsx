import Link from 'next/link'
import { getSiteConfig } from '@/lib/data'
import { Instagram, Twitter, Mail } from 'lucide-react'

export default function Footer() {
  const siteConfig = getSiteConfig()
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-surface border-t border-border mt-20">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-2">
            <Link href="/" className="inline-block mb-4">
              <h3 className="font-heading text-2xl font-bold text-text-primary">
                Femmepod
              </h3>
            </Link>
            <p className="text-text-secondary mb-6 max-w-md">
              {siteConfig.artist.bio}
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              <a
                href={`mailto:${siteConfig.links.email}`}
                className="text-text-secondary hover:text-accent-primary transition-colors"
                aria-label="Email"
              >
                <Mail size={20} />
              </a>
              {siteConfig.links.instagram && (
                <a
                  href={siteConfig.links.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-text-secondary hover:text-accent-primary transition-colors"
                  aria-label="Instagram"
                >
                  <Instagram size={20} />
                </a>
              )}
              {siteConfig.links.twitter && (
                <a
                  href={siteConfig.links.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-text-secondary hover:text-accent-primary transition-colors"
                  aria-label="Twitter"
                >
                  <Twitter size={20} />
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-heading text-lg font-semibold text-text-primary mb-4">
              Quick Links
            </h4>
            <ul className="space-y-2">
              <li>
                <Link href="/designs" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Design Gallery
                </Link>
              </li>
              <li>
                <Link href="/shops" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Official Shops
                </Link>
              </li>
              <li>
                <Link href="/custom-commissions" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Custom Work
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Community
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-heading text-lg font-semibold text-text-primary mb-4">
              Support
            </h4>
            <ul className="space-y-2">
              <li>
                <Link href="/quality-and-partners" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Quality Guarantee
                </Link>
              </li>
              <li>
                <a
                  href={`mailto:${siteConfig.links.email}`}
                  className="text-text-secondary hover:text-accent-primary transition-colors"
                >
                  Contact Artist
                </a>
              </li>
              <li>
                <Link href="/custom-commissions" className="text-text-secondary hover:text-accent-primary transition-colors">
                  Commission Info
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-text-secondary text-sm">
            © {currentYear} Femmepod. All rights reserved.
          </p>
          
          <div className="flex items-center space-x-6 mt-4 md:mt-0">
            <span className="text-text-secondary text-sm">
              Made with ❤️ for the modern chimera
            </span>
          </div>
        </div>
      </div>
    </footer>
  )
}
