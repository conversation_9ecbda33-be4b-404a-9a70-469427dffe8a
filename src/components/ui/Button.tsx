'use client'

import { ButtonHTMLAttributes, ReactNode } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  children: ReactNode
  href?: string
  external?: boolean
}

export default function Button({
  variant = 'primary',
  size = 'md',
  children,
  className,
  href,
  external = false,
  ...props
}: ButtonProps) {
  const baseClasses = `
    inline-flex items-center justify-center
    border-0 font-medium uppercase tracking-wider
    cursor-pointer transition-all duration-200
    relative overflow-hidden
  `

  const variants = {
    primary: `
      bg-accent-primary text-background
      hover:bg-accent-secondary hover:-translate-y-0.5
      hover:shadow-[0_8px_25px_rgba(255,0,122,0.3)]
    `,
    secondary: `
      bg-transparent text-accent-primary border-2 border-accent-primary
      hover:bg-accent-primary hover:text-background
    `,
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  }

  const classes = cn(
    baseClasses,
    variants[variant],
    sizes[size],
    className
  )

  if (href) {
    return (
      <motion.a
        href={href}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
        className={classes}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {children}
      </motion.a>
    )
  }

  return (
    <motion.button
      className={classes}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      {...props}
    >
      {children}
    </motion.button>
  )
}
