'use client'

import { Design } from '@/lib/types'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { fadeInUpVariants } from '@/lib/utils'

interface DesignCardProps {
  design: Design
  priority?: boolean
}

export default function DesignCard({ design, priority = false }: DesignCardProps) {
  return (
    <motion.div
      variants={fadeInUpVariants}
      whileHover={{ y: -8 }}
      className="group relative overflow-hidden bg-surface border border-border transition-all duration-300 hover:border-accent-primary"
    >
      <Link href={`/designs/${design.slug}`}>
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={design.image}
            alt={design.name}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-110"
            priority={priority}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Featured badge */}
          {design.featured && (
            <div className="absolute top-4 right-4 bg-accent-primary text-background px-2 py-1 text-xs font-medium uppercase tracking-wider">
              Featured
            </div>
          )}
        </div>

        <div className="p-6">
          <h3 className="font-heading text-xl font-semibold text-text-primary mb-2 group-hover:text-accent-primary transition-colors">
            {design.name}
          </h3>
          
          <p className="text-text-secondary text-sm mb-4 line-clamp-2">
            {design.description}
          </p>
          
          {/* Themes */}
          <div className="flex flex-wrap gap-2 mb-4">
            {design.themes.slice(0, 3).map((theme) => (
              <span
                key={theme}
                className="px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border"
              >
                {theme}
              </span>
            ))}
            {design.themes.length > 3 && (
              <span className="px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border">
                +{design.themes.length - 3}
              </span>
            )}
          </div>

          {/* Shop count */}
          <div className="flex items-center justify-between">
            <span className="text-text-secondary text-sm">
              Available on {design.shopLinks.length} platform{design.shopLinks.length !== 1 ? 's' : ''}
            </span>
            
            <motion.div
              className="text-accent-primary opacity-0 group-hover:opacity-100 transition-opacity"
              whileHover={{ x: 4 }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </motion.div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
