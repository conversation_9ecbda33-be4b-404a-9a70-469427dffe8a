'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ShopLink } from '@/lib/types'
import { getCountryFromIP, fadeInUpVariants } from '@/lib/utils'
import { ExternalLink, MapPin } from 'lucide-react'

interface WhereToBuyProps {
  shopLinks: ShopLink[]
  designName: string
}

export default function WhereToBuy({ shopLinks, designName }: WhereToBuyProps) {
  const [userCountry, setUserCountry] = useState<string>('US')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const getUserLocation = async () => {
      try {
        const country = await getCountryFromIP()
        setUserCountry(country)
      } catch (error) {
        console.error('Failed to get user location:', error)
      } finally {
        setIsLoading(false)
      }
    }

    getUserLocation()
  }, [])

  // Group shop links by platform
  const groupedShops = shopLinks.reduce((acc, link) => {
    if (!acc[link.platform]) {
      acc[link.platform] = []
    }
    acc[link.platform].push(link)
    return acc
  }, {} as Record<string, ShopLink[]>)

  // Determine recommended shop based on user's country
  const getRecommendedShop = (platformLinks: ShopLink[]) => {
    // First, try to find exact country match
    const exactMatch = platformLinks.find(link => 
      link.region.toLowerCase() === userCountry.toLowerCase()
    )
    if (exactMatch) return exactMatch

    // Then try regional matches
    const regionMatches = {
      'US': ['US', 'North America', 'Global'],
      'CA': ['CA', 'North America', 'US', 'Global'],
      'GB': ['UK', 'EU', 'Europe', 'Global'],
      'DE': ['EU', 'Europe', 'Global'],
      'FR': ['EU', 'Europe', 'Global'],
      'IN': ['India', 'South Asia', 'Global'],
      'AU': ['AU', 'Australia', 'Global'],
    }

    const userRegions = regionMatches[userCountry as keyof typeof regionMatches] || ['Global']
    
    for (const region of userRegions) {
      const match = platformLinks.find(link => 
        link.region.toLowerCase().includes(region.toLowerCase())
      )
      if (match) return match
    }

    // Fallback to first available
    return platformLinks[0]
  }

  return (
    <motion.div
      variants={fadeInUpVariants}
      className="bg-surface border border-border p-8"
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-accent-primary rounded-full flex items-center justify-center">
          <ExternalLink size={16} className="text-background" />
        </div>
        <h3 className="font-heading text-2xl font-semibold text-text-primary">
          Where to Buy "{designName}"
        </h3>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"></div>
          <span className="ml-3 text-text-secondary">Finding best options for your location...</span>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedShops).map(([platform, platformLinks]) => {
            const recommendedShop = getRecommendedShop(platformLinks)
            
            return (
              <div key={platform} className="border border-border p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {recommendedShop.logo && (
                      <img 
                        src={recommendedShop.logo} 
                        alt={`${platform} logo`}
                        className="w-8 h-8 object-contain"
                      />
                    )}
                    <h4 className="font-heading text-xl font-semibold text-text-primary">
                      {platform}
                    </h4>
                  </div>
                  
                  {recommendedShop.region !== 'Global' && (
                    <div className="flex items-center gap-2 px-3 py-1 bg-accent-primary/10 border border-accent-primary/20 rounded-full">
                      <MapPin size={14} className="text-accent-primary" />
                      <span className="text-accent-primary text-sm font-medium">
                        Recommended for {recommendedShop.region}
                      </span>
                    </div>
                  )}
                </div>

                <div className="grid gap-3">
                  {platformLinks.map((link, index) => (
                    <motion.a
                      key={index}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`
                        flex items-center justify-between p-4 border transition-all duration-200
                        hover:border-accent-primary hover:bg-accent-primary/5
                        ${link === recommendedShop 
                          ? 'border-accent-primary bg-accent-primary/5' 
                          : 'border-border'
                        }
                      `}
                      whileHover={{ x: 4 }}
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-text-primary font-medium">
                            Buy on {platform}
                          </span>
                          <span className="text-text-secondary">
                            ({link.region})
                          </span>
                        </div>
                        {link === recommendedShop && (
                          <span className="px-2 py-1 bg-accent-primary text-background text-xs font-medium uppercase tracking-wider rounded">
                            Best for you
                          </span>
                        )}
                      </div>
                      
                      <ExternalLink size={16} className="text-text-secondary" />
                    </motion.a>
                  ))}
                </div>
              </div>
            )
          })}

          <div className="bg-background border border-border p-4 rounded">
            <p className="text-text-secondary text-sm">
              <strong>Quality Guarantee:</strong> All designs are printed on premium materials 
              with our vector art guarantee. Each platform offers different product types and 
              shipping options to serve you better.
            </p>
          </div>
        </div>
      )}
    </motion.div>
  )
}
