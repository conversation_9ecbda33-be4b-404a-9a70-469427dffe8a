'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import But<PERSON> from './Button'
import { fadeInUpVariants } from '@/lib/utils'
import { Send, CheckCircle, AlertCircle } from 'lucide-react'

interface FormData {
  name: string
  email: string
  projectType: string
  budget: string
  timeline: string
  description: string
}

export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    projectType: '',
    budget: '',
    timeline: '',
    description: '',
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      // Create mailto link with form data
      const subject = `Commission Inquiry from ${formData.name}`
      const body = `
Name: ${formData.name}
Email: ${formData.email}
Project Type: ${formData.projectType}
Budget: ${formData.budget}
Timeline: ${formData.timeline}

Project Description:
${formData.description}
      `.trim()
      
      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
      
      // Open email client
      window.location.href = mailtoLink
      
      // Simulate success after a short delay
      setTimeout(() => {
        setSubmitStatus('success')
        setIsSubmitting(false)
        
        // Reset form after success
        setTimeout(() => {
          setFormData({
            name: '',
            email: '',
            projectType: '',
            budget: '',
            timeline: '',
            description: '',
          })
          setSubmitStatus('idle')
        }, 3000)
      }, 1000)
      
    } catch (error) {
      setSubmitStatus('error')
      setIsSubmitting(false)
    }
  }

  if (submitStatus === 'success') {
    return (
      <motion.div
        variants={fadeInUpVariants}
        className="bg-surface border border-accent-primary p-8 text-center"
      >
        <CheckCircle size={48} className="text-accent-primary mx-auto mb-4" />
        <h3 className="font-heading text-2xl font-semibold text-text-primary mb-4">
          Thank You!
        </h3>
        <p className="text-text-secondary">
          Your commission inquiry has been prepared. Your email client should have opened 
          with a pre-filled message. If not, please send your details directly to 
          <a href="mailto:<EMAIL>" className="text-accent-primary hover:underline ml-1">
            <EMAIL>
          </a>
        </p>
      </motion.div>
    )
  }

  return (
    <motion.form
      variants={fadeInUpVariants}
      onSubmit={handleSubmit}
      className="bg-surface border border-border p-8 space-y-6"
    >
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-text-primary font-medium mb-2">
            Your Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors"
            placeholder="Enter your full name"
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-text-primary font-medium mb-2">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="projectType" className="block text-text-primary font-medium mb-2">
            Project Type *
          </label>
          <select
            id="projectType"
            name="projectType"
            value={formData.projectType}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors"
          >
            <option value="">Select project type</option>
            <option value="character-design">Character Design</option>
            <option value="illustration">Custom Illustration</option>
            <option value="logo-branding">Logo & Branding</option>
            <option value="concept-art">Concept Art</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="budget" className="block text-text-primary font-medium mb-2">
            Budget Range
          </label>
          <select
            id="budget"
            name="budget"
            value={formData.budget}
            onChange={handleChange}
            className="w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors"
          >
            <option value="">Select budget range</option>
            <option value="under-500">Under $500</option>
            <option value="500-1000">$500 - $1,000</option>
            <option value="1000-2500">$1,000 - $2,500</option>
            <option value="2500-plus">$2,500+</option>
            <option value="discuss">Let's discuss</option>
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="timeline" className="block text-text-primary font-medium mb-2">
          Timeline
        </label>
        <select
          id="timeline"
          name="timeline"
          value={formData.timeline}
          onChange={handleChange}
          className="w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors"
        >
          <option value="">Select timeline</option>
          <option value="rush-1-week">Rush (1 week) - +50% fee</option>
          <option value="standard-2-4-weeks">Standard (2-4 weeks)</option>
          <option value="flexible-1-2-months">Flexible (1-2 months)</option>
          <option value="no-rush">No rush - when you're available</option>
        </select>
      </div>

      <div>
        <label htmlFor="description" className="block text-text-primary font-medium mb-2">
          Project Description *
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          required
          rows={6}
          className="w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors resize-vertical"
          placeholder="Describe your vision in detail. Include style preferences, themes, colors, size requirements, intended use, and any reference materials you have in mind..."
        />
      </div>

      <div className="bg-background border border-border p-4 rounded">
        <p className="text-text-secondary text-sm">
          <strong>Note:</strong> Commission rates start at $75/hour with a minimum project size of $200. 
          Rush orders (under 2 weeks) include a 50% urgency fee. All artwork includes commercial usage rights 
          and source files upon completion.
        </p>
      </div>

      <Button
        type="submit"
        variant="primary"
        size="lg"
        disabled={isSubmitting}
        className="w-full"
      >
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-background mr-2"></div>
            Preparing Email...
          </>
        ) : (
          <>
            <Send size={20} className="mr-2" />
            Send Commission Inquiry
          </>
        )}
      </Button>
    </motion.form>
  )
}
