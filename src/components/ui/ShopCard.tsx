'use client'

import { Shop } from '@/lib/types'
import { motion } from 'framer-motion'
import { ExternalLink, MapPin } from 'lucide-react'
import { fadeInUpVariants } from '@/lib/utils'

interface ShopCardProps {
  shop: Shop
}

export default function ShopCard({ shop }: ShopCardProps) {
  return (
    <motion.div
      variants={fadeInUpVariants}
      className="group bg-surface border border-border p-6 transition-all duration-300 hover:border-accent-primary hover:shadow-lg"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-4">
          {shop.logo && (
            <div className="w-12 h-12 bg-background border border-border rounded-full flex items-center justify-center p-2">
              <img 
                src={shop.logo} 
                alt={`${shop.name} logo`}
                className="w-full h-full object-contain"
              />
            </div>
          )}
          <div>
            <h3 className="font-heading text-xl font-semibold text-text-primary group-hover:text-accent-primary transition-colors">
              {shop.name}
            </h3>
            {shop.featured && (
              <span className="inline-block px-2 py-1 bg-accent-primary text-background text-xs font-medium uppercase tracking-wider rounded mt-1">
                Featured
              </span>
            )}
          </div>
        </div>
        
        <motion.div
          className="text-text-secondary group-hover:text-accent-primary transition-colors"
          whileHover={{ scale: 1.1 }}
        >
          <ExternalLink size={20} />
        </motion.div>
      </div>

      <p className="text-text-secondary mb-4 leading-relaxed">
        {shop.description}
      </p>

      {/* Regions */}
      <div className="flex items-center gap-2 mb-6">
        <MapPin size={16} className="text-text-secondary" />
        <div className="flex flex-wrap gap-2">
          {shop.regions.map((region) => (
            <span
              key={region}
              className="px-2 py-1 bg-background border border-border text-text-secondary text-xs uppercase tracking-wider"
            >
              {region}
            </span>
          ))}
        </div>
      </div>

      {/* CTA Button */}
      <motion.a
        href={shop.url}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center justify-center w-full px-6 py-3 bg-accent-primary text-background font-medium uppercase tracking-wider transition-all duration-200 hover:bg-accent-secondary hover:-translate-y-1 hover:shadow-lg"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <span>Visit {shop.name}</span>
        <ExternalLink size={16} className="ml-2" />
      </motion.a>
    </motion.div>
  )
}
