'use client'

import { motion } from 'framer-motion'
import But<PERSON> from './Button'
import { staggerContainer, fadeInUpVariants } from '@/lib/utils'

interface HeroProps {
  title: string
  subtitle?: string
  description?: string
  primaryCTA?: {
    text: string
    href: string
  }
  secondaryCTA?: {
    text: string
    href: string
  }
  backgroundImage?: string
}

export default function Hero({
  title,
  subtitle,
  description,
  primaryCTA,
  secondaryCTA,
  backgroundImage,
}: HeroProps) {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      {backgroundImage && (
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background/90 to-surface/50" />
      
      {/* Content */}
      <motion.div
        className="relative z-10 container mx-auto px-4 text-center"
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
      >
        {subtitle && (
          <motion.p
            variants={fadeInUpVariants}
            className="text-accent-primary font-medium uppercase tracking-wider mb-4"
          >
            {subtitle}
          </motion.p>
        )}
        
        <motion.h1
          variants={fadeInUpVariants}
          className="font-heading text-h1 font-bold text-text-primary mb-6 leading-tight"
        >
          {title}
        </motion.h1>
        
        {description && (
          <motion.p
            variants={fadeInUpVariants}
            className="text-xl text-text-secondary max-w-2xl mx-auto mb-8 leading-relaxed"
          >
            {description}
          </motion.p>
        )}
        
        {(primaryCTA || secondaryCTA) && (
          <motion.div
            variants={fadeInUpVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            {primaryCTA && (
              <Button
                variant="primary"
                size="lg"
                href={primaryCTA.href}
              >
                {primaryCTA.text}
              </Button>
            )}
            
            {secondaryCTA && (
              <Button
                variant="secondary"
                size="lg"
                href={secondaryCTA.href}
              >
                {secondaryCTA.text}
              </Button>
            )}
          </motion.div>
        )}
      </motion.div>
      
      {/* Animated Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-accent-primary rounded-full"
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute top-1/3 right-1/3 w-1 h-1 bg-accent-secondary rounded-full"
          animate={{
            y: [0, -15, 0],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
          }}
        />
        <motion.div
          className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-accent-primary rounded-full"
          animate={{
            y: [0, -10, 0],
            opacity: [0.4, 1, 0.4],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 0.5,
          }}
        />
      </div>
    </section>
  )
}
