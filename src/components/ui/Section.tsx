'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { staggerContainer, fadeInUpVariants } from '@/lib/utils'

interface SectionProps {
  children: ReactNode
  title?: string
  subtitle?: string
  className?: string
  containerClassName?: string
  animate?: boolean
  id?: string
}

export default function Section({
  children,
  title,
  subtitle,
  className = '',
  containerClassName = '',
  animate = true,
  id,
}: SectionProps) {
  const content = (
    <section className={`py-20 ${className}`} id={id}>
      <div className={`container mx-auto px-4 ${containerClassName}`}>
        {(title || subtitle) && (
          <div className="text-center mb-16">
            {subtitle && (
              <motion.p
                variants={animate ? fadeInUpVariants : undefined}
                className="text-accent-primary font-medium uppercase tracking-wider mb-4"
              >
                {subtitle}
              </motion.p>
            )}
            {title && (
              <motion.h2
                variants={animate ? fadeInUpVariants : undefined}
                className="font-heading text-h2 font-semibold text-text-primary"
              >
                {title}
              </motion.h2>
            )}
          </div>
        )}
        {children}
      </div>
    </section>
  )

  if (animate) {
    return (
      <motion.div
        variants={staggerContainer}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
      >
        {content}
      </motion.div>
    )
  }

  return content
}
