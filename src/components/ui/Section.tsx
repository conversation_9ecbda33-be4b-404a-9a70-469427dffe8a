'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { staggerContainer, fadeInUpVariants } from '@/lib/utils'

interface SectionProps {
  children: ReactNode
  title?: string
  subtitle?: string
  className?: string
  containerClassName?: string
  animate?: boolean
  id?: string
}

export default function Section({
  children,
  title,
  subtitle,
  className = '',
  containerClassName = '',
  animate = true,
  id,
}: SectionProps) {
  const content = (
    <section className={`py-20 ${className}`} id={id}>
      <div className={`container mx-auto px-4 ${containerClassName}`}>
        {(title || subtitle) && (
          <div className="text-center mb-16">
            {subtitle && (
              <p className="text-accent-primary font-medium uppercase tracking-wider mb-4">
                {subtitle}
              </p>
            )}
            {title && (
              <h2 className="font-heading text-h2 font-semibold text-text-primary">
                {title}
              </h2>
            )}
          </div>
        )}
        {children}
      </div>
    </section>
  )

  return content
}
