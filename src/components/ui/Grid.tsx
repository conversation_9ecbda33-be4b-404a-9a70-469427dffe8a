'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { staggerContainer, fadeInUpVariants } from '@/lib/utils'

interface GridProps {
  children: ReactNode
  columns?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: number
  className?: string
  animate?: boolean
}

export default function Grid({
  children,
  columns = { sm: 1, md: 2, lg: 3, xl: 3 },
  gap = 6,
  className = '',
  animate = true,
}: GridProps) {
  const gridClasses = `
    grid
    grid-cols-${columns.sm || 1}
    md:grid-cols-${columns.md || 2}
    lg:grid-cols-${columns.lg || 3}
    xl:grid-cols-${columns.xl || 3}
    gap-${gap}
    ${className}
  `

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}
