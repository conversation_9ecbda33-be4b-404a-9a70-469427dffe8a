'use client'

import { Testimonial } from '@/lib/types'
import { motion } from 'framer-motion'
import { Star } from 'lucide-react'
import { fadeInUpVariants } from '@/lib/utils'

interface TestimonialCardProps {
  testimonial: Testimonial
}

export default function TestimonialCard({ testimonial }: TestimonialCardProps) {
  return (
    <motion.div
      variants={fadeInUpVariants}
      className="bg-surface border border-border p-6 h-full flex flex-col"
    >
      {/* Rating */}
      <div className="flex items-center mb-4">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            size={16}
            className={`${
              i < testimonial.rating
                ? 'text-accent-primary fill-current'
                : 'text-border'
            }`}
          />
        ))}
      </div>

      {/* Content */}
      <blockquote className="text-text-primary mb-6 flex-grow">
        &ldquo;{testimonial.content}&rdquo;
      </blockquote>

      {/* Author */}
      <div className="flex items-center justify-between">
        <div>
          <cite className="font-medium text-text-primary not-italic">
            {testimonial.name}
          </cite>
          {testimonial.location && (
            <p className="text-text-secondary text-sm">
              {testimonial.location}
            </p>
          )}
        </div>
        
        {testimonial.platform && (
          <div className="text-accent-primary text-sm font-medium">
            via {testimonial.platform}
          </div>
        )}
      </div>
    </motion.div>
  )
}
