import { getSiteConfig } from '@/lib/data'
import { Design } from '@/lib/types'

interface StructuredDataProps {
  type: 'website' | 'person' | 'imageObject' | 'breadcrumbList'
  design?: Design
  breadcrumbs?: Array<{ name: string; url: string }>
}

export default function StructuredData({ type, design, breadcrumbs }: StructuredDataProps) {
  const siteConfig = getSiteConfig()

  const generateWebsiteSchema = () => ({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    author: {
      '@type': 'Person',
      name: siteConfig.artist.name,
      description: siteConfig.artist.bio,
      url: siteConfig.url,
      sameAs: [
        siteConfig.links.instagram,
        siteConfig.links.twitter,
      ].filter(Boolean),
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/designs?search={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  })

  const generatePersonSchema = () => ({
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: siteConfig.artist.name,
    description: siteConfig.artist.bio,
    url: siteConfig.url,
    image: siteConfig.ogImage,
    sameAs: [
      siteConfig.links.instagram,
      siteConfig.links.twitter,
    ].filter(Boolean),
    jobTitle: 'Digital Artist & Designer',
    worksFor: {
      '@type': 'Organization',
      name: siteConfig.name,
      url: siteConfig.url,
    },
    knowsAbout: [
      'Digital Art',
      'Illustration',
      'Character Design',
      'Anime Art',
      'Feminist Art',
      'Vector Art',
    ],
  })

  const generateImageObjectSchema = (design: Design) => ({
    '@context': 'https://schema.org',
    '@type': 'ImageObject',
    name: design.name,
    description: design.description,
    url: `${siteConfig.url}${design.image}`,
    author: {
      '@type': 'Person',
      name: siteConfig.artist.name,
      url: siteConfig.url,
    },
    creator: {
      '@type': 'Person',
      name: siteConfig.artist.name,
      url: siteConfig.url,
    },
    dateCreated: design.createdAt,
    keywords: design.themes.join(', '),
    license: 'All rights reserved',
    copyrightHolder: {
      '@type': 'Person',
      name: siteConfig.artist.name,
    },
    contentUrl: `${siteConfig.url}${design.image}`,
    thumbnailUrl: `${siteConfig.url}${design.image}`,
  })

  const generateBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  })

  let schema = {}

  switch (type) {
    case 'website':
      schema = generateWebsiteSchema()
      break
    case 'person':
      schema = generatePersonSchema()
      break
    case 'imageObject':
      if (design) {
        schema = generateImageObjectSchema(design)
      }
      break
    case 'breadcrumbList':
      if (breadcrumbs) {
        schema = generateBreadcrumbSchema(breadcrumbs)
      }
      break
    default:
      return null
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2),
      }}
    />
  )
}
