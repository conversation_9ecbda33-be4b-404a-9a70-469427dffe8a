// Design data structure
export interface ShopLink {
  platform: string
  url: string
  region: string
  logo?: string
}

export interface Design {
  id: string
  name: string
  slug: string
  description: string
  themes: string[]
  image: string
  shopLinks: ShopLink[]
  featured?: boolean
  createdAt?: string
}

// Shop information
export interface Shop {
  id: string
  name: string
  description: string
  url: string
  logo: string
  regions: string[]
  featured?: boolean
}

// Testimonial structure
export interface Testimonial {
  id: string
  name: string
  content: string
  rating: number
  platform?: string
  image?: string
  location?: string
}

// Commission service structure
export interface CommissionService {
  id: string
  name: string
  description: string
  price: string
  deliveryTime: string
  features: string[]
}

// SEO Meta data structure
export interface SEOMeta {
  title: string
  description: string
  keywords: string[]
  ogImage?: string
  canonicalUrl?: string
}

// Page data structure
export interface PageData {
  slug: string
  title: string
  meta: SEOMeta
  content?: Record<string, unknown>
}

// Site configuration
export interface SiteConfig {
  name: string
  description: string
  url: string
  ogImage: string
  links: {
    email: string
    instagram?: string
    twitter?: string
  }
  artist: {
    name: string
    bio: string
    location: string
  }
  stats: {
    customerSatisfaction: string
    fiveStarReviews: string
    designsCreated: string
    countriesShipped: string
  }
}
