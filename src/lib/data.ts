import { Design, Shop, Testimonial, SiteConfig } from './types'

// Import JSON data
import designsData from '@/data/designs.json'
import shopsData from '@/data/shops.json'
import testimonialsData from '@/data/testimonials.json'
import siteConfigData from '@/data/site-config.json'

// Type the imported data
const designs = designsData as Design[]
const shops = shopsData as Shop[]
const testimonials = testimonialsData as Testimonial[]
const siteConfig = siteConfigData as SiteConfig

// Design-related functions
export function getAllDesigns(): Design[] {
  return designs.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
}

export function getFeaturedDesigns(): Design[] {
  return designs.filter(design => design.featured).slice(0, 6)
}

export function getDesignBySlug(slug: string): Design | undefined {
  return designs.find(design => design.slug === slug)
}

export function getDesignsByTheme(theme: string): Design[] {
  return designs.filter(design => design.themes.includes(theme.toLowerCase()))
}

export function getAllThemes(): string[] {
  const allThemes = designs.flatMap(design => design.themes)
  return [...new Set(allThemes)].sort()
}

export function getRelatedDesigns(currentDesign: Design, limit: number = 3): Design[] {
  const related = designs
    .filter(design => 
      design.id !== currentDesign.id && 
      design.themes.some(theme => currentDesign.themes.includes(theme))
    )
    .slice(0, limit)
  
  // If we don't have enough related designs, fill with other designs
  if (related.length < limit) {
    const additional = designs
      .filter(design => 
        design.id !== currentDesign.id && 
        !related.some(r => r.id === design.id)
      )
      .slice(0, limit - related.length)
    
    return [...related, ...additional]
  }
  
  return related
}

// Shop-related functions
export function getAllShops(): Shop[] {
  return shops
}

export function getFeaturedShops(): Shop[] {
  return shops.filter(shop => shop.featured)
}

export function getShopById(id: string): Shop | undefined {
  return shops.find(shop => shop.id === id)
}

export function getShopsByRegion(region: string): Shop[] {
  return shops.filter(shop => shop.regions.includes(region))
}

// Testimonial-related functions
export function getAllTestimonials(): Testimonial[] {
  return testimonials
}

export function getRandomTestimonials(count: number = 3): Testimonial[] {
  const shuffled = [...testimonials].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// Site configuration
export function getSiteConfig(): SiteConfig {
  return siteConfig
}

// SEO and metadata functions
export function generateDesignMeta(design: Design) {
  return {
    title: `${design.name} | Femmepod Design Portfolio`,
    description: `View the "${design.name}" artwork by Femmepod. ${design.description}`,
    keywords: [design.name, 'Femmepod', ...design.themes, 'art', 'design', 'illustration'],
    ogImage: design.image,
  }
}

export function generatePageMeta(page: string) {
  const baseMeta = {
    'home': {
      title: 'Femmepod: Official Portfolio of Artist Jia',
      description: 'The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.',
      keywords: ['Femmepod', 'Jia artist', 'designer portfolio', 'anime art', 'feminist art'],
    },
    'designs': {
      title: 'Design Gallery | Femmepod Portfolio',
      description: 'Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.',
      keywords: ['Femmepod designs', 'art portfolio', 'illustration gallery', 'anime art'],
    },
    'shops': {
      title: 'Official Femmepod Shops (US & India)',
      description: 'Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.',
      keywords: ['Femmepod shops', 'where to buy Femmepod', 'Redbubble', 'Threadless'],
    },
    'custom-commissions': {
      title: 'Custom Illustrations & Design Commissions by Femmepod',
      description: 'Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.',
      keywords: ['custom illustration', 'commission artist', 'hire illustrator', 'custom art'],
    },
  }

  return baseMeta[page as keyof typeof baseMeta] || baseMeta.home
}
