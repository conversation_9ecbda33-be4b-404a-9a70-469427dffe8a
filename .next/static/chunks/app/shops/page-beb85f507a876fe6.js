(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[647],{191:(e,r,s)=>{Promise.resolve().then(s.bind(s,6927)),Promise.resolve().then(s.bind(s,3741)),Promise.resolve().then(s.bind(s,1325)),Promise.resolve().then(s.bind(s,1514)),Promise.resolve().then(s.bind(s,4203))},1325:(e,r,s)=>{"use strict";s.d(r,{default:()=>i});var a=s(5155),t=s(2605),n=s(9434);function i(e){let{children:r,columns:s={sm:1,md:2,lg:3,xl:3},gap:i=6,className:c="",animate:l=!0}=e,d="\n    grid\n    grid-cols-".concat(s.sm||1,"\n    md:grid-cols-").concat(s.md||2,"\n    lg:grid-cols-").concat(s.lg||3,"\n    xl:grid-cols-").concat(s.xl||3,"\n    gap-").concat(i,"\n    ").concat(c,"\n  ");return l?(0,a.jsx)(t.P.div,{className:d,variants:n.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},children:r}):(0,a.jsx)("div",{className:d,children:r})}},3786:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4203:(e,r,s)=>{"use strict";s.d(r,{default:()=>l});var a=s(5155),t=s(2605),n=s(3786),i=s(4516),c=s(9434);function l(e){let{shop:r}=e;return(0,a.jsxs)(t.P.div,{variants:c.HM,className:"group bg-surface border border-border p-6 transition-all duration-300 hover:border-accent-primary hover:shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[r.logo&&(0,a.jsx)("div",{className:"w-12 h-12 bg-background border border-border rounded-full flex items-center justify-center p-2",children:(0,a.jsx)("img",{src:r.logo,alt:"".concat(r.name," logo"),className:"w-full h-full object-contain"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary group-hover:text-accent-primary transition-colors",children:r.name}),r.featured&&(0,a.jsx)("span",{className:"inline-block px-2 py-1 bg-accent-primary text-background text-xs font-medium uppercase tracking-wider rounded mt-1",children:"Featured"})]})]}),(0,a.jsx)(t.P.div,{className:"text-text-secondary group-hover:text-accent-primary transition-colors",whileHover:{scale:1.1},children:(0,a.jsx)(n.A,{size:20})})]}),(0,a.jsx)("p",{className:"text-text-secondary mb-4 leading-relaxed",children:r.description}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,a.jsx)(i.A,{size:16,className:"text-text-secondary"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:r.regions.map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-background border border-border text-text-secondary text-xs uppercase tracking-wider",children:e},e))})]}),(0,a.jsxs)(t.P.a,{href:r.url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center w-full px-6 py-3 bg-accent-primary text-background font-medium uppercase tracking-wider transition-all duration-200 hover:bg-accent-secondary hover:-translate-y-1 hover:shadow-lg",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsxs)("span",{children:["Visit ",r.name]}),(0,a.jsx)(n.A,{size:16,className:"ml-2"})]})]})}},4516:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}},e=>{e.O(0,[769,303,441,964,358],()=>e(e.s=191)),_N_E=e.O()}]);