(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[602],{242:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,3063,23)),Promise.resolve().then(t.bind(t,6927)),Promise.resolve().then(t.bind(t,3741)),Promise.resolve().then(t.bind(t,2549)),Promise.resolve().then(t.bind(t,1325)),Promise.resolve().then(t.bind(t,1514)),Promise.resolve().then(t.bind(t,1791))},1325:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(5155),a=t(2605),n=t(9434);function i(e){let{children:r,columns:t={sm:1,md:2,lg:3,xl:3},gap:i=6,className:o="",animate:c=!0}=e,l="\n    grid\n    grid-cols-".concat(t.sm||1,"\n    md:grid-cols-").concat(t.md||2,"\n    lg:grid-cols-").concat(t.lg||3,"\n    xl:grid-cols-").concat(t.xl||3,"\n    gap-").concat(i,"\n    ").concat(o,"\n  ");return c?(0,s.jsx)(a.P.div,{className:l,variants:n.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},children:r}):(0,s.jsx)("div",{className:l,children:r})}},1791:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(5155),a=t(2115),n=t(2605),i=t(9434),o=t(3786),c=t(4516);function l(e){let{shopLinks:r,designName:t}=e,[l,d]=(0,a.useState)("US"),[m,x]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{try{let e=await (0,i._t)();d(e)}catch(e){console.error("Failed to get user location:",e)}finally{x(!1)}})()},[]);let p=r.reduce((e,r)=>(e[r.platform]||(e[r.platform]=[]),e[r.platform].push(r),e),{});return(0,s.jsxs)(n.P.div,{variants:i.HM,className:"bg-surface border border-border p-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-accent-primary rounded-full flex items-center justify-center",children:(0,s.jsx)(o.A,{size:16,className:"text-background"})}),(0,s.jsxs)("h3",{className:"font-heading text-2xl font-semibold text-text-primary",children:["Where to Buy “",t,"”"]})]}),m?(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-accent-primary"}),(0,s.jsx)("span",{className:"ml-3 text-text-secondary",children:"Finding best options for your location..."})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[Object.entries(p).map(e=>{let[r,t]=e,a=(e=>{let r=e.find(e=>e.region.toLowerCase()===l.toLowerCase());if(r)return r;for(let r of({US:["US","North America","Global"],CA:["CA","North America","US","Global"],GB:["UK","EU","Europe","Global"],DE:["EU","Europe","Global"],FR:["EU","Europe","Global"],IN:["India","South Asia","Global"],AU:["AU","Australia","Global"]})[l]||["Global"]){let t=e.find(e=>e.region.toLowerCase().includes(r.toLowerCase()));if(t)return t}return e[0]})(t);return(0,s.jsxs)("div",{className:"border border-border p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[a.logo&&(0,s.jsx)("img",{src:a.logo,alt:"".concat(r," logo"),className:"w-8 h-8 object-contain"}),(0,s.jsx)("h4",{className:"font-heading text-xl font-semibold text-text-primary",children:r})]}),"Global"!==a.region&&(0,s.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1 bg-accent-primary/10 border border-accent-primary/20 rounded-full",children:[(0,s.jsx)(c.A,{size:14,className:"text-accent-primary"}),(0,s.jsxs)("span",{className:"text-accent-primary text-sm font-medium",children:["Recommended for ",a.region]})]})]}),(0,s.jsx)("div",{className:"grid gap-3",children:t.map((e,t)=>(0,s.jsxs)(n.P.a,{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"\n                        flex items-center justify-between p-4 border transition-all duration-200\n                        hover:border-accent-primary hover:bg-accent-primary/5\n                        ".concat(e===a?"border-accent-primary bg-accent-primary/5":"border-border","\n                      "),whileHover:{x:4},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-text-primary font-medium",children:["Buy on ",r]}),(0,s.jsxs)("span",{className:"text-text-secondary",children:["(",e.region,")"]})]}),e===a&&(0,s.jsx)("span",{className:"px-2 py-1 bg-accent-primary text-background text-xs font-medium uppercase tracking-wider rounded",children:"Best for you"})]}),(0,s.jsx)(o.A,{size:16,className:"text-text-secondary"})]},t))})]},r)}),(0,s.jsx)("div",{className:"bg-background border border-border p-4 rounded",children:(0,s.jsxs)("p",{className:"text-text-secondary text-sm",children:[(0,s.jsx)("strong",{children:"Quality Guarantee:"})," All designs are printed on premium materials with our vector art guarantee. Each platform offers different product types and shipping options to serve you better."]})})]})]})}},2549:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(5155),a=t(2605),n=t(6766),i=t(6874),o=t.n(i),c=t(9434);function l(e){let{design:r,priority:t=!1}=e;return(0,s.jsx)(a.P.div,{variants:c.HM,whileHover:{y:-8},className:"group relative overflow-hidden bg-surface border border-border transition-all duration-300 hover:border-accent-primary",children:(0,s.jsxs)(o(),{href:"/designs/".concat(r.slug),children:[(0,s.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,s.jsx)(n.default,{src:r.image,alt:r.name,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",priority:t,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.featured&&(0,s.jsx)("div",{className:"absolute top-4 right-4 bg-accent-primary text-background px-2 py-1 text-xs font-medium uppercase tracking-wider",children:"Featured"})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-2 group-hover:text-accent-primary transition-colors",children:r.name}),(0,s.jsx)("p",{className:"text-text-secondary text-sm mb-4 line-clamp-2",children:r.description}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[r.themes.slice(0,3).map(e=>(0,s.jsx)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:e},e)),r.themes.length>3&&(0,s.jsxs)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:["+",r.themes.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-text-secondary text-sm",children:["Available on ",r.shopLinks.length," platform",1!==r.shopLinks.length?"s":""]}),(0,s.jsx)(a.P.div,{className:"text-accent-primary opacity-0 group-hover:opacity-100 transition-opacity",whileHover:{x:4},children:(0,s.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M7 17L17 7M17 7H7M17 7V17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})]})})}},3786:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4516:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}},e=>{e.O(0,[769,766,303,441,964,358],()=>e(e.s=242)),_N_E=e.O()}]);