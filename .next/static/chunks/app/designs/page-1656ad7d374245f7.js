(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[202],{1007:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(5155),a=s(2115),n=s(6927),i=s(1514),l=s(1325),o=s(2549),c=s(3741),d=s(8236),x=s(9946);let m=(0,x.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),u=(0,x.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);function h(){let[e,t]=(0,a.useState)(""),[s,x]=(0,a.useState)("all"),[h,p]=(0,a.useState)("newest"),g=(0,d.N0)(),b=(0,d.Cg)(),f=(0,a.useMemo)(()=>{let t=g;switch(e&&(t=t.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase())||t.themes.some(t=>t.toLowerCase().includes(e.toLowerCase())))),"all"!==s&&(t=t.filter(e=>e.themes.includes(s))),h){case"newest":return t.sort((e,t)=>new Date(t.createdAt||"").getTime()-new Date(e.createdAt||"").getTime());case"oldest":return t.sort((e,t)=>new Date(e.createdAt||"").getTime()-new Date(t.createdAt||"").getTime());case"name":return t.sort((e,t)=>e.name.localeCompare(t.name));default:return t}},[g,e,s,h]);return(0,r.jsxs)(n.default,{children:[(0,r.jsx)(i.default,{title:"Design Gallery",subtitle:"Complete Collection",className:"bg-gradient-to-br from-background to-surface",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto text-center",children:(0,r.jsx)("p",{className:"text-xl text-text-secondary leading-relaxed",children:"Explore the complete collection of Femmepod designs. Each piece tells a unique story of empowerment, strength, and artistic vision."})})}),(0,r.jsxs)(i.default,{animate:!1,className:"py-8 bg-surface border-y border-border",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 items-center justify-between",children:[(0,r.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,r.jsx)(m,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary",size:20}),(0,r.jsx)("input",{type:"text",placeholder:"Search designs...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(u,{size:20,className:"text-text-secondary"}),(0,r.jsxs)("select",{value:s,onChange:e=>x(e.target.value),className:"px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors",children:[(0,r.jsx)("option",{value:"all",children:"All Themes"}),b.map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("span",{className:"text-text-secondary text-sm",children:"Sort by:"}),(0,r.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors",children:[(0,r.jsx)("option",{value:"newest",children:"Newest First"}),(0,r.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,r.jsx)("option",{value:"name",children:"Name A-Z"})]})]})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-text-secondary",children:["Showing ",f.length," of ",g.length," designs","all"!==s&&' in "'.concat(s,'"'),e&&' matching "'.concat(e,'"')]})})]}),(0,r.jsx)(i.default,{animate:!1,children:f.length>0?(0,r.jsx)(l.default,{columns:{sm:1,md:2,lg:3,xl:4},children:f.map(e=>(0,r.jsx)(o.default,{design:e},e.id))}):(0,r.jsxs)("div",{className:"text-center py-20",children:[(0,r.jsx)("div",{className:"text-6xl mb-6",children:"\uD83C\uDFA8"}),(0,r.jsx)("h3",{className:"font-heading text-2xl font-semibold text-text-primary mb-4",children:"No designs found"}),(0,r.jsx)("p",{className:"text-text-secondary mb-8",children:"Try adjusting your search terms or filters to find what you're looking for."}),(0,r.jsx)(c.default,{onClick:()=>{t(""),x("all"),p("newest")},variant:"secondary",children:"Clear Filters"})]})}),(0,r.jsx)(i.default,{title:"Can't Find What You're Looking For?",subtitle:"Custom Commissions",className:"bg-gradient-to-br from-surface to-background",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,r.jsx)("p",{className:"text-xl text-text-secondary mb-8 leading-relaxed",children:"Every design in this gallery started as a vision. Let's bring your unique vision to life with a custom commission that's perfectly tailored to your story."}),(0,r.jsx)(c.default,{href:"/custom-commissions",variant:"primary",size:"lg",children:"Commission Custom Art"})]})})]})}},1325:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(5155),a=s(2605),n=s(9434);function i(e){let{children:t,columns:s={sm:1,md:2,lg:3,xl:3},gap:i=6,className:l="",animate:o=!0}=e,c="\n    grid\n    grid-cols-".concat(s.sm||1,"\n    md:grid-cols-").concat(s.md||2,"\n    lg:grid-cols-").concat(s.lg||3,"\n    xl:grid-cols-").concat(s.xl||3,"\n    gap-").concat(i,"\n    ").concat(l,"\n  ");return o?(0,r.jsx)(a.P.div,{className:c,variants:n.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},children:t}):(0,r.jsx)("div",{className:c,children:t})}},2270:(e,t,s)=>{Promise.resolve().then(s.bind(s,1007))},2549:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(5155),a=s(2605),n=s(6766),i=s(6874),l=s.n(i),o=s(9434);function c(e){let{design:t,priority:s=!1}=e;return(0,r.jsx)(a.P.div,{variants:o.HM,whileHover:{y:-8},className:"group relative overflow-hidden bg-surface border border-border transition-all duration-300 hover:border-accent-primary",children:(0,r.jsxs)(l(),{href:"/designs/".concat(t.slug),children:[(0,r.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,r.jsx)(n.default,{src:t.image,alt:t.name,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",priority:s,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),t.featured&&(0,r.jsx)("div",{className:"absolute top-4 right-4 bg-accent-primary text-background px-2 py-1 text-xs font-medium uppercase tracking-wider",children:"Featured"})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-2 group-hover:text-accent-primary transition-colors",children:t.name}),(0,r.jsx)("p",{className:"text-text-secondary text-sm mb-4 line-clamp-2",children:t.description}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.themes.slice(0,3).map(e=>(0,r.jsx)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:e},e)),t.themes.length>3&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:["+",t.themes.length-3]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-text-secondary text-sm",children:["Available on ",t.shopLinks.length," platform",1!==t.shopLinks.length?"s":""]}),(0,r.jsx)(a.P.div,{className:"text-accent-primary opacity-0 group-hover:opacity-100 transition-opacity",whileHover:{x:4},children:(0,r.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{d:"M7 17L17 7M17 7H7M17 7V17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})]})})}}},e=>{e.O(0,[769,766,303,441,964,358],()=>e(e.s=2270)),_N_E=e.O()}]);