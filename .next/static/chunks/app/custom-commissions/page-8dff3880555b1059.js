(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[899],{3573:(e,r,t)=>{"use strict";t.d(r,{default:()=>m});var n=t(5155),i=t(2115),o=t(2605),a=t(3741),s=t(9434),l=t(9946);let c=(0,l.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),d=(0,l.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function m(){let[e,r]=(0,i.useState)({name:"",email:"",projectType:"",budget:"",timeline:"",description:""}),[t,l]=(0,i.useState)(!1),[m,u]=(0,i.useState)("idle"),p=e=>{r(r=>({...r,[e.target.name]:e.target.value}))},h=async t=>{t.preventDefault(),l(!0);try{let t="Commission Inquiry from ".concat(e.name),n="\nName: ".concat(e.name,"\nEmail: ").concat(e.email,"\nProject Type: ").concat(e.projectType,"\nBudget: ").concat(e.budget,"\nTimeline: ").concat(e.timeline,"\n\nProject Description:\n").concat(e.description,"\n      ").trim(),i="mailto:<EMAIL>?subject=".concat(encodeURIComponent(t),"&body=").concat(encodeURIComponent(n));window.location.href=i,setTimeout(()=>{u("success"),l(!1),setTimeout(()=>{r({name:"",email:"",projectType:"",budget:"",timeline:"",description:""}),u("idle")},3e3)},1e3)}catch(e){u("error"),l(!1)}};return"success"===m?(0,n.jsxs)(o.P.div,{variants:s.HM,className:"bg-surface border border-accent-primary p-8 text-center",children:[(0,n.jsx)(c,{size:48,className:"text-accent-primary mx-auto mb-4"}),(0,n.jsx)("h3",{className:"font-heading text-2xl font-semibold text-text-primary mb-4",children:"Thank You!"}),(0,n.jsxs)("p",{className:"text-text-secondary",children:["Your commission inquiry has been prepared. Your email client should have opened with a pre-filled message. If not, please send your details directly to",(0,n.jsx)("a",{href:"mailto:<EMAIL>",className:"text-accent-primary hover:underline ml-1",children:"<EMAIL>"})]})]}):(0,n.jsxs)(o.P.form,{variants:s.HM,onSubmit:h,className:"bg-surface border border-border p-8 space-y-6",children:[(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-text-primary font-medium mb-2",children:"Your Name *"}),(0,n.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:p,required:!0,className:"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors",placeholder:"Enter your full name"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-text-primary font-medium mb-2",children:"Email Address *"}),(0,n.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:p,required:!0,className:"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors",placeholder:"<EMAIL>"})]})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"projectType",className:"block text-text-primary font-medium mb-2",children:"Project Type *"}),(0,n.jsxs)("select",{id:"projectType",name:"projectType",value:e.projectType,onChange:p,required:!0,className:"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors",children:[(0,n.jsx)("option",{value:"",children:"Select project type"}),(0,n.jsx)("option",{value:"character-design",children:"Character Design"}),(0,n.jsx)("option",{value:"illustration",children:"Custom Illustration"}),(0,n.jsx)("option",{value:"logo-branding",children:"Logo & Branding"}),(0,n.jsx)("option",{value:"concept-art",children:"Concept Art"}),(0,n.jsx)("option",{value:"other",children:"Other"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"budget",className:"block text-text-primary font-medium mb-2",children:"Budget Range"}),(0,n.jsxs)("select",{id:"budget",name:"budget",value:e.budget,onChange:p,className:"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors",children:[(0,n.jsx)("option",{value:"",children:"Select budget range"}),(0,n.jsx)("option",{value:"under-500",children:"Under $500"}),(0,n.jsx)("option",{value:"500-1000",children:"$500 - $1,000"}),(0,n.jsx)("option",{value:"1000-2500",children:"$1,000 - $2,500"}),(0,n.jsx)("option",{value:"2500-plus",children:"$2,500+"}),(0,n.jsx)("option",{value:"discuss",children:"Let's discuss"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"timeline",className:"block text-text-primary font-medium mb-2",children:"Timeline"}),(0,n.jsxs)("select",{id:"timeline",name:"timeline",value:e.timeline,onChange:p,className:"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors",children:[(0,n.jsx)("option",{value:"",children:"Select timeline"}),(0,n.jsx)("option",{value:"rush-1-week",children:"Rush (1 week) - +50% fee"}),(0,n.jsx)("option",{value:"standard-2-4-weeks",children:"Standard (2-4 weeks)"}),(0,n.jsx)("option",{value:"flexible-1-2-months",children:"Flexible (1-2 months)"}),(0,n.jsx)("option",{value:"no-rush",children:"No rush - when you're available"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"description",className:"block text-text-primary font-medium mb-2",children:"Project Description *"}),(0,n.jsx)("textarea",{id:"description",name:"description",value:e.description,onChange:p,required:!0,rows:6,className:"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors resize-vertical",placeholder:"Describe your vision in detail. Include style preferences, themes, colors, size requirements, intended use, and any reference materials you have in mind..."})]}),(0,n.jsx)("div",{className:"bg-background border border-border p-4 rounded",children:(0,n.jsxs)("p",{className:"text-text-secondary text-sm",children:[(0,n.jsx)("strong",{children:"Note:"})," Commission rates start at $75/hour with a minimum project size of $200. Rush orders (under 2 weeks) include a 50% urgency fee. All artwork includes commercial usage rights and source files upon completion."]})}),(0,n.jsx)(a.default,{type:"submit",variant:"primary",size:"lg",disabled:t,className:"w-full",children:t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-background mr-2"}),"Preparing Email..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d,{size:20,className:"mr-2"}),"Send Commission Inquiry"]})})]})}},6149:(e,r,t)=>{Promise.resolve().then(t.bind(t,6927)),Promise.resolve().then(t.bind(t,3573)),Promise.resolve().then(t.bind(t,1514))}},e=>{e.O(0,[769,303,441,964,358],()=>e(e.s=6149)),_N_E=e.O()}]);