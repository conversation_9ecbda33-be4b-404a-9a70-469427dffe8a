{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X } from 'lucide-react'\n\nconst navItems = [\n  { name: 'Home', href: '/' },\n  { name: 'Designs', href: '/designs' },\n  { name: 'Shops', href: '/shops' },\n  { name: 'Quality & Partners', href: '/quality-and-partners' },\n  { name: 'Custom Commissions', href: '/custom-commissions' },\n  { name: 'Community', href: '/community' },\n]\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [scrolled, setScrolled] = useState(false)\n  const pathname = usePathname()\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  useEffect(() => {\n    setIsOpen(false)\n  }, [pathname])\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled ? 'bg-background/95 backdrop-blur-md border-b border-border' : 'bg-transparent'\n      }`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      <nav className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"group\">\n            <motion.div\n              className=\"font-heading text-2xl font-bold text-text-primary group-hover:text-accent-primary transition-colors\"\n              whileHover={{ scale: 1.05 }}\n            >\n              Femmepod\n            </motion.div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative text-sm font-medium uppercase tracking-wider transition-colors hover:text-accent-primary ${\n                  pathname === item.href ? 'text-accent-primary' : 'text-text-primary'\n                }`}\n              >\n                {item.name}\n                {pathname === item.href && (\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-accent-primary\"\n                    layoutId=\"activeTab\"\n                  />\n                )}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden text-text-primary hover:text-accent-primary transition-colors\"\n            onClick={() => setIsOpen(!isOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-b border-border\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"container mx-auto px-4 py-6\">\n                <div className=\"flex flex-col space-y-4\">\n                  {navItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`text-sm font-medium uppercase tracking-wider transition-colors hover:text-accent-primary ${\n                        pathname === item.href ? 'text-accent-primary' : 'text-text-primary'\n                      }`}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n    </motion.header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAsB,MAAM;IAAwB;IAC5D;QAAE,MAAM;QAAsB,MAAM;IAAsB;IAC1D;QAAE,MAAM;QAAa,MAAM;IAAa;CACzC;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,UAAU;QACZ;+BAAG;QAAC;KAAS;IAEb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,AAAC,+DAEX,OADC,WAAW,6DAA6D;QAE1E,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;0CAC3B;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,qGAEX,OADC,aAAa,KAAK,IAAI,GAAG,wBAAwB;;wCAGlD,KAAK,IAAI;wCACT,aAAa,KAAK,IAAI,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAS;;;;;;;mCAVR,KAAK,IAAI;;;;;;;;;;sCAkBpB,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,cAAW;sCAEV,uBAAS,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAK5C,6LAAC,4LAAA,CAAA,kBAAe;8BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,AAAC,4FAEX,OADC,aAAa,KAAK,IAAI,GAAG,wBAAwB;kDAGlD,KAAK,IAAI;uCANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBpC;GArGwB;;QAGL,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/lib/data.ts"], "sourcesContent": ["import { Design, Shop, Testimonial, SiteConfig } from './types'\n\n// Import JSON data\nimport designsData from '@/data/designs.json'\nimport shopsData from '@/data/shops.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport siteConfigData from '@/data/site-config.json'\n\n// Type the imported data\nconst designs = designsData as Design[]\nconst shops = shopsData as Shop[]\nconst testimonials = testimonialsData as Testimonial[]\nconst siteConfig = siteConfigData as SiteConfig\n\n// Design-related functions\nexport function getAllDesigns(): Design[] {\n  return designs.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())\n}\n\nexport function getFeaturedDesigns(): Design[] {\n  return designs.filter(design => design.featured).slice(0, 6)\n}\n\nexport function getDesignBySlug(slug: string): Design | undefined {\n  return designs.find(design => design.slug === slug)\n}\n\nexport function getDesignsByTheme(theme: string): Design[] {\n  return designs.filter(design => design.themes.includes(theme.toLowerCase()))\n}\n\nexport function getAllThemes(): string[] {\n  const allThemes = designs.flatMap(design => design.themes)\n  return [...new Set(allThemes)].sort()\n}\n\nexport function getRelatedDesigns(currentDesign: Design, limit: number = 3): Design[] {\n  const related = designs\n    .filter(design => \n      design.id !== currentDesign.id && \n      design.themes.some(theme => currentDesign.themes.includes(theme))\n    )\n    .slice(0, limit)\n  \n  // If we don't have enough related designs, fill with other designs\n  if (related.length < limit) {\n    const additional = designs\n      .filter(design => \n        design.id !== currentDesign.id && \n        !related.some(r => r.id === design.id)\n      )\n      .slice(0, limit - related.length)\n    \n    return [...related, ...additional]\n  }\n  \n  return related\n}\n\n// Shop-related functions\nexport function getAllShops(): Shop[] {\n  return shops\n}\n\nexport function getFeaturedShops(): Shop[] {\n  return shops.filter(shop => shop.featured)\n}\n\nexport function getShopById(id: string): Shop | undefined {\n  return shops.find(shop => shop.id === id)\n}\n\nexport function getShopsByRegion(region: string): Shop[] {\n  return shops.filter(shop => shop.regions.includes(region))\n}\n\n// Testimonial-related functions\nexport function getAllTestimonials(): Testimonial[] {\n  return testimonials\n}\n\nexport function getRandomTestimonials(count: number = 3): Testimonial[] {\n  const shuffled = [...testimonials].sort(() => 0.5 - Math.random())\n  return shuffled.slice(0, count)\n}\n\n// Site configuration\nexport function getSiteConfig(): SiteConfig {\n  return siteConfig\n}\n\n// SEO and metadata functions\nexport function generateDesignMeta(design: Design) {\n  return {\n    title: `${design.name} | Femmepod Design Portfolio`,\n    description: `View the \"${design.name}\" artwork by Femmepod. ${design.description}`,\n    keywords: [design.name, 'Femmepod', ...design.themes, 'art', 'design', 'illustration'],\n    ogImage: design.image,\n  }\n}\n\nexport function generatePageMeta(page: string) {\n  const baseMeta = {\n    'home': {\n      title: 'Femmepod: Official Portfolio of Artist Jia',\n      description: 'The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.',\n      keywords: ['Femmepod', 'Jia artist', 'designer portfolio', 'anime art', 'feminist art'],\n    },\n    'designs': {\n      title: 'Design Gallery | Femmepod Portfolio',\n      description: 'Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.',\n      keywords: ['Femmepod designs', 'art portfolio', 'illustration gallery', 'anime art'],\n    },\n    'shops': {\n      title: 'Official Femmepod Shops (US & India)',\n      description: 'Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.',\n      keywords: ['Femmepod shops', 'where to buy Femmepod', 'Redbubble', 'Threadless'],\n    },\n    'custom-commissions': {\n      title: 'Custom Illustrations & Design Commissions by Femmepod',\n      description: 'Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.',\n      keywords: ['custom illustration', 'commission artist', 'hire illustrator', 'custom art'],\n    },\n  }\n\n  return baseMeta[page as keyof typeof baseMeta] || baseMeta.home\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;;;;;AAEA,yBAAyB;AACzB,MAAM,UAAU,8FAAA,CAAA,UAAW;AAC3B,MAAM,QAAQ,4FAAA,CAAA,UAAS;AACvB,MAAM,eAAe,mGAAA,CAAA,UAAgB;AACrC,MAAM,aAAa,qGAAA,CAAA,UAAc;AAG1B,SAAS;IACd,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;AAC3G;AAEO,SAAS;IACd,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,KAAK,CAAC,GAAG;AAC5D;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,WAAW;AAC1E;AAEO,SAAS;IACd,MAAM,YAAY,QAAQ,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;IACzD,OAAO;WAAI,IAAI,IAAI;KAAW,CAAC,IAAI;AACrC;AAEO,SAAS,kBAAkB,aAAqB;QAAE,QAAA,iEAAgB;IACvE,MAAM,UAAU,QACb,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,cAAc,MAAM,CAAC,QAAQ,CAAC,SAE3D,KAAK,CAAC,GAAG;IAEZ,mEAAmE;IACnE,IAAI,QAAQ,MAAM,GAAG,OAAO;QAC1B,MAAM,aAAa,QAChB,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE,GAEtC,KAAK,CAAC,GAAG,QAAQ,QAAQ,MAAM;QAElC,OAAO;eAAI;eAAY;SAAW;IACpC;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC3C;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC;AACpD;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;QAAsB,QAAA,iEAAgB;IACpD,MAAM,WAAW;WAAI;KAAa,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC/D,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,SAAS;IACd,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc;IAC/C,OAAO;QACL,OAAO,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;QACtB,aAAa,AAAC,aAAiD,OAArC,OAAO,IAAI,EAAC,2BAA4C,OAAnB,OAAO,WAAW;QACjF,UAAU;YAAC,OAAO,IAAI;YAAE;eAAe,OAAO,MAAM;YAAE;YAAO;YAAU;SAAe;QACtF,SAAS,OAAO,KAAK;IACvB;AACF;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,WAAW;QACf,QAAQ;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAY;gBAAc;gBAAsB;gBAAa;aAAe;QACzF;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAiB;gBAAwB;aAAY;QACtF;QACA,SAAS;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAkB;gBAAyB;gBAAa;aAAa;QAClF;QACA,sBAAsB;YACpB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAuB;gBAAqB;gBAAoB;aAAa;QAC1F;IACF;IAEA,OAAO,QAAQ,CAAC,KAA8B,IAAI,SAAS,IAAI;AACjE", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { getSiteConfig } from '@/lib/data'\nimport { Instagram, Twitter, Mail } from 'lucide-react'\n\nexport default function Footer() {\n  const siteConfig = getSiteConfig()\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-surface border-t border-border mt-20\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-2\">\n            <Link href=\"/\" className=\"inline-block mb-4\">\n              <h3 className=\"font-heading text-2xl font-bold text-text-primary\">\n                Femmepod\n              </h3>\n            </Link>\n            <p className=\"text-text-secondary mb-6 max-w-md\">\n              {siteConfig.artist.bio}\n            </p>\n            \n            {/* Social Links */}\n            <div className=\"flex space-x-4\">\n              <a\n                href={`mailto:${siteConfig.links.email}`}\n                className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                aria-label=\"Email\"\n              >\n                <Mail size={20} />\n              </a>\n              {siteConfig.links.instagram && (\n                <a\n                  href={siteConfig.links.instagram}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                  aria-label=\"Instagram\"\n                >\n                  <Instagram size={20} />\n                </a>\n              )}\n              {siteConfig.links.twitter && (\n                <a\n                  href={siteConfig.links.twitter}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                  aria-label=\"Twitter\"\n                >\n                  <Twitter size={20} />\n                </a>\n              )}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"font-heading text-lg font-semibold text-text-primary mb-4\">\n              Quick Links\n            </h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/designs\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Design Gallery\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/shops\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Official Shops\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/custom-commissions\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Custom Work\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/community\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Community\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h4 className=\"font-heading text-lg font-semibold text-text-primary mb-4\">\n              Support\n            </h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/quality-and-partners\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Quality Guarantee\n                </Link>\n              </li>\n              <li>\n                <a\n                  href={`mailto:${siteConfig.links.email}`}\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                >\n                  Contact Artist\n                </a>\n              </li>\n              <li>\n                <Link href=\"/custom-commissions\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Commission Info\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-text-secondary text-sm\">\n            © {currentYear} Femmepod. All rights reserved.\n          </p>\n          \n          <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n            <span className=\"text-text-secondary text-sm\">\n              Made with ❤️ for the modern chimera\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;;;;;;8CAIpE,6LAAC;oCAAE,WAAU;8CACV,WAAW,MAAM,CAAC,GAAG;;;;;;8CAIxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAM,AAAC,UAAgC,OAAvB,WAAW,KAAK,CAAC,KAAK;4CACtC,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;wCAEb,WAAW,KAAK,CAAC,SAAS,kBACzB,6LAAC;4CACC,MAAM,WAAW,KAAK,CAAC,SAAS;4CAChC,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,+MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;wCAGpB,WAAW,KAAK,CAAC,OAAO,kBACvB,6LAAC;4CACC,MAAM,WAAW,KAAK,CAAC,OAAO;4CAC9B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAkE;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAkE;;;;;;;;;;;sDAIlG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAAkE;;;;;;;;;;;sDAI/G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAkE;;;;;;;;;;;;;;;;;;;;;;;sCAQ1G,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAkE;;;;;;;;;;;sDAIjH,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,AAAC,UAAgC,OAAvB,WAAW,KAAK,CAAC,KAAK;gDACtC,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAAkE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAA8B;gCACtC;gCAAY;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;KA7HwB", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/CustomCursor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { motion } from 'framer-motion'\n\nexport default function CustomCursor() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n  const [isHovering, setIsHovering] = useState(false)\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n    }\n\n    const handleMouseEnter = () => setIsHovering(true)\n    const handleMouseLeave = () => setIsHovering(false)\n\n    // Add event listeners for mouse movement\n    window.addEventListener('mousemove', updateMousePosition)\n\n    // Add event listeners for hover states on interactive elements\n    const interactiveElements = document.querySelectorAll('a, button, [role=\"button\"]')\n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter)\n      el.addEventListener('mouseleave', handleMouseLeave)\n    })\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition)\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter)\n        el.removeEventListener('mouseleave', handleMouseLeave)\n      })\n    }\n  }, [])\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 w-5 h-5 bg-accent-primary rounded-full pointer-events-none z-[9999] mix-blend-difference\"\n      animate={{\n        x: mousePosition.x - 10,\n        y: mousePosition.y - 10,\n        scale: isHovering ? 2 : 1,\n      }}\n      transition={{\n        type: \"spring\",\n        stiffness: 500,\n        damping: 28,\n        mass: 0.5,\n      }}\n      style={{\n        backgroundColor: isHovering ? 'var(--color-accent-secondary)' : 'var(--color-accent-primary)',\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;8DAAsB,CAAC;oBAC3B,iBAAiB;wBAAE,GAAG,EAAE,OAAO;wBAAE,GAAG,EAAE,OAAO;oBAAC;gBAChD;;YAEA,MAAM;2DAAmB,IAAM,cAAc;;YAC7C,MAAM;2DAAmB,IAAM,cAAc;;YAE7C,yCAAyC;YACzC,OAAO,gBAAgB,CAAC,aAAa;YAErC,+DAA+D;YAC/D,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;YACtD,oBAAoB,OAAO;0CAAC,CAAA;oBAC1B,GAAG,gBAAgB,CAAC,cAAc;oBAClC,GAAG,gBAAgB,CAAC,cAAc;gBACpC;;YAEA;0CAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;oBACxC,oBAAoB,OAAO;kDAAC,CAAA;4BAC1B,GAAG,mBAAmB,CAAC,cAAc;4BACrC,GAAG,mBAAmB,CAAC,cAAc;wBACvC;;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YACP,GAAG,cAAc,CAAC,GAAG;YACrB,GAAG,cAAc,CAAC,GAAG;YACrB,OAAO,aAAa,IAAI;QAC1B;QACA,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;QACA,OAAO;YACL,iBAAiB,aAAa,kCAAkC;QAClE;;;;;;AAGN;GAlDwB;KAAA", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport Navigation from './Navigation'\nimport Footer from './Footer'\nimport CustomCursor from '../ui/CustomCursor'\n\ninterface LayoutProps {\n  children: ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background text-text-primary\">\n      <CustomCursor />\n      <Navigation />\n      <main className=\"pt-20\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;IAC7B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAA,CAAA,UAAY;;;;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BACX,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAXwB", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Utility function for scroll-triggered animations\nexport const fadeInUpVariants = {\n  hidden: {\n    opacity: 0,\n    y: 30,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: \"easeOut\" as const,\n    },\n  },\n}\n\n// Utility function for staggered animations\nexport const staggerContainer = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n}\n\n// Utility function for geolocation-based recommendations\nexport const getCountryFromIP = async (): Promise<string> => {\n  try {\n    const response = await fetch('https://ipapi.co/json/')\n    const data = await response.json()\n    return data.country_code || 'US'\n  } catch (error) {\n    console.error('Failed to get country from IP:', error)\n    return 'US' // Default to US\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,mBAAmB;IAC9B,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,KAAK,gBAAgB;;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { staggerContainer, fadeInUpVariants } from '@/lib/utils'\n\ninterface SectionProps {\n  children: ReactNode\n  title?: string\n  subtitle?: string\n  className?: string\n  containerClassName?: string\n  animate?: boolean\n  id?: string\n}\n\nexport default function Section({\n  children,\n  title,\n  subtitle,\n  className = '',\n  containerClassName = '',\n  animate = true,\n  id,\n}: SectionProps) {\n  const content = (\n    <section className={`py-20 ${className}`} id={id}>\n      <div className={`container mx-auto px-4 ${containerClassName}`}>\n        {(title || subtitle) && (\n          <div className=\"text-center mb-16\">\n            {subtitle && (\n              <motion.p\n                variants={animate ? fadeInUpVariants : undefined}\n                className=\"text-accent-primary font-medium uppercase tracking-wider mb-4\"\n              >\n                {subtitle}\n              </motion.p>\n            )}\n            {title && (\n              <motion.h2\n                variants={animate ? fadeInUpVariants : undefined}\n                className=\"font-heading text-h2 font-semibold text-text-primary\"\n              >\n                {title}\n              </motion.h2>\n            )}\n          </div>\n        )}\n        {children}\n      </div>\n    </section>\n  )\n\n  if (animate) {\n    return (\n      <motion.div\n        variants={staggerContainer}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, margin: \"-100px\" }}\n      >\n        {content}\n      </motion.div>\n    )\n  }\n\n  return content\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAgBe,SAAS,QAAQ,KAQjB;QARiB,EAC9B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,UAAU,IAAI,EACd,EAAE,EACW,GARiB;IAS9B,MAAM,wBACJ,6LAAC;QAAQ,WAAW,AAAC,SAAkB,OAAV;QAAa,IAAI;kBAC5C,cAAA,6LAAC;YAAI,WAAW,AAAC,0BAA4C,OAAnB;;gBACvC,CAAC,SAAS,QAAQ,mBACjB,6LAAC;oBAAI,WAAU;;wBACZ,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU,UAAU,sHAAA,CAAA,mBAAgB,GAAG;4BACvC,WAAU;sCAET;;;;;;wBAGJ,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU,UAAU,sHAAA,CAAA,mBAAgB,GAAG;4BACvC,WAAU;sCAET;;;;;;;;;;;;gBAKR;;;;;;;;;;;;IAKP,IAAI,SAAS;QACX,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU,sHAAA,CAAA,mBAAgB;YAC1B,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAS;sBAExC;;;;;;IAGP;IAEA,OAAO;AACT;KAnDwB", "debugId": null}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { motion, HTMLMotionProps } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends Omit<HTMLMotionProps<'button'>, 'children'> {\n  variant?: 'primary' | 'secondary'\n  size?: 'sm' | 'md' | 'lg'\n  children: ReactNode\n  href?: string\n  external?: boolean\n}\n\nexport default function Button({\n  variant = 'primary',\n  size = 'md',\n  children,\n  className,\n  href,\n  external = false,\n  ...props\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center\n    border-0 font-medium uppercase tracking-wider\n    cursor-pointer transition-all duration-200\n    relative overflow-hidden\n  `\n\n  const variants = {\n    primary: `\n      bg-accent-primary text-background\n      hover:bg-accent-secondary hover:-translate-y-0.5\n      hover:shadow-[0_8px_25px_rgba(255,0,122,0.3)]\n    `,\n    secondary: `\n      bg-transparent text-accent-primary border-2 border-accent-primary\n      hover:bg-accent-primary hover:text-background\n    `,\n  }\n\n  const sizes = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg',\n  }\n\n  const classes = cn(\n    baseClasses,\n    variants[variant],\n    sizes[size],\n    className\n  )\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={external ? '_blank' : undefined}\n        rel={external ? 'noopener noreferrer' : undefined}\n        className={classes}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n      >\n        {children}\n      </motion.a>\n    )\n  }\n\n  return (\n    <motion.button\n      className={classes}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      {...props}\n    >\n      {children}\n    </motion.button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAce,SAAS,OAAO,KAQjB;QARiB,EAC7B,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,WAAW,KAAK,EAChB,GAAG,OACS,GARiB;IAS7B,MAAM,cAAe;IAOrB,MAAM,WAAW;QACf,SAAU;QAKV,WAAY;IAId;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,MAAM;QACR,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;YACP,MAAM;YACN,QAAQ,WAAW,WAAW;YAC9B,KAAK,WAAW,wBAAwB;YACxC,WAAW;YACX,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;sBAEvB;;;;;;IAGP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW;QACX,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACvB,GAAG,KAAK;kBAER;;;;;;AAGP;KAlEwB", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/ContactForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport But<PERSON> from './Button'\nimport { fadeInUpVariants } from '@/lib/utils'\nimport { Send, CheckCircle, AlertCircle } from 'lucide-react'\n\ninterface FormData {\n  name: string\n  email: string\n  projectType: string\n  budget: string\n  timeline: string\n  description: string\n}\n\nexport default function ContactForm() {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    projectType: '',\n    budget: '',\n    timeline: '',\n    description: '',\n  })\n  \n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    \n    try {\n      // Create mailto link with form data\n      const subject = `Commission Inquiry from ${formData.name}`\n      const body = `\nName: ${formData.name}\nEmail: ${formData.email}\nProject Type: ${formData.projectType}\nBudget: ${formData.budget}\nTimeline: ${formData.timeline}\n\nProject Description:\n${formData.description}\n      `.trim()\n      \n      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`\n      \n      // Open email client\n      window.location.href = mailtoLink\n      \n      // Simulate success after a short delay\n      setTimeout(() => {\n        setSubmitStatus('success')\n        setIsSubmitting(false)\n        \n        // Reset form after success\n        setTimeout(() => {\n          setFormData({\n            name: '',\n            email: '',\n            projectType: '',\n            budget: '',\n            timeline: '',\n            description: '',\n          })\n          setSubmitStatus('idle')\n        }, 3000)\n      }, 1000)\n      \n    } catch (error) {\n      setSubmitStatus('error')\n      setIsSubmitting(false)\n    }\n  }\n\n  if (submitStatus === 'success') {\n    return (\n      <motion.div\n        variants={fadeInUpVariants}\n        className=\"bg-surface border border-accent-primary p-8 text-center\"\n      >\n        <CheckCircle size={48} className=\"text-accent-primary mx-auto mb-4\" />\n        <h3 className=\"font-heading text-2xl font-semibold text-text-primary mb-4\">\n          Thank You!\n        </h3>\n        <p className=\"text-text-secondary\">\n          Your commission inquiry has been prepared. Your email client should have opened \n          with a pre-filled message. If not, please send your details directly to \n          <a href=\"mailto:<EMAIL>\" className=\"text-accent-primary hover:underline ml-1\">\n            <EMAIL>\n          </a>\n        </p>\n      </motion.div>\n    )\n  }\n\n  return (\n    <motion.form\n      variants={fadeInUpVariants}\n      onSubmit={handleSubmit}\n      className=\"bg-surface border border-border p-8 space-y-6\"\n    >\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        <div>\n          <label htmlFor=\"name\" className=\"block text-text-primary font-medium mb-2\">\n            Your Name *\n          </label>\n          <input\n            type=\"text\"\n            id=\"name\"\n            name=\"name\"\n            value={formData.name}\n            onChange={handleChange}\n            required\n            className=\"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors\"\n            placeholder=\"Enter your full name\"\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"email\" className=\"block text-text-primary font-medium mb-2\">\n            Email Address *\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            required\n            className=\"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors\"\n            placeholder=\"<EMAIL>\"\n          />\n        </div>\n      </div>\n\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        <div>\n          <label htmlFor=\"projectType\" className=\"block text-text-primary font-medium mb-2\">\n            Project Type *\n          </label>\n          <select\n            id=\"projectType\"\n            name=\"projectType\"\n            value={formData.projectType}\n            onChange={handleChange}\n            required\n            className=\"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors\"\n          >\n            <option value=\"\">Select project type</option>\n            <option value=\"character-design\">Character Design</option>\n            <option value=\"illustration\">Custom Illustration</option>\n            <option value=\"logo-branding\">Logo & Branding</option>\n            <option value=\"concept-art\">Concept Art</option>\n            <option value=\"other\">Other</option>\n          </select>\n        </div>\n        \n        <div>\n          <label htmlFor=\"budget\" className=\"block text-text-primary font-medium mb-2\">\n            Budget Range\n          </label>\n          <select\n            id=\"budget\"\n            name=\"budget\"\n            value={formData.budget}\n            onChange={handleChange}\n            className=\"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors\"\n          >\n            <option value=\"\">Select budget range</option>\n            <option value=\"under-500\">Under $500</option>\n            <option value=\"500-1000\">$500 - $1,000</option>\n            <option value=\"1000-2500\">$1,000 - $2,500</option>\n            <option value=\"2500-plus\">$2,500+</option>\n            <option value=\"discuss\">Let's discuss</option>\n          </select>\n        </div>\n      </div>\n\n      <div>\n        <label htmlFor=\"timeline\" className=\"block text-text-primary font-medium mb-2\">\n          Timeline\n        </label>\n        <select\n          id=\"timeline\"\n          name=\"timeline\"\n          value={formData.timeline}\n          onChange={handleChange}\n          className=\"w-full px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors\"\n        >\n          <option value=\"\">Select timeline</option>\n          <option value=\"rush-1-week\">Rush (1 week) - +50% fee</option>\n          <option value=\"standard-2-4-weeks\">Standard (2-4 weeks)</option>\n          <option value=\"flexible-1-2-months\">Flexible (1-2 months)</option>\n          <option value=\"no-rush\">No rush - when you're available</option>\n        </select>\n      </div>\n\n      <div>\n        <label htmlFor=\"description\" className=\"block text-text-primary font-medium mb-2\">\n          Project Description *\n        </label>\n        <textarea\n          id=\"description\"\n          name=\"description\"\n          value={formData.description}\n          onChange={handleChange}\n          required\n          rows={6}\n          className=\"w-full px-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors resize-vertical\"\n          placeholder=\"Describe your vision in detail. Include style preferences, themes, colors, size requirements, intended use, and any reference materials you have in mind...\"\n        />\n      </div>\n\n      <div className=\"bg-background border border-border p-4 rounded\">\n        <p className=\"text-text-secondary text-sm\">\n          <strong>Note:</strong> Commission rates start at $75/hour with a minimum project size of $200. \n          Rush orders (under 2 weeks) include a 50% urgency fee. All artwork includes commercial usage rights \n          and source files upon completion.\n        </p>\n      </div>\n\n      <Button\n        type=\"submit\"\n        variant=\"primary\"\n        size=\"lg\"\n        disabled={isSubmitting}\n        className=\"w-full\"\n      >\n        {isSubmitting ? (\n          <>\n            <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-background mr-2\"></div>\n            Preparing Email...\n          </>\n        ) : (\n          <>\n            <Send size={20} className=\"mr-2\" />\n            Send Commission Inquiry\n          </>\n        )}\n      </Button>\n    </motion.form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,oCAAoC;YACpC,MAAM,UAAU,AAAC,2BAAwC,OAAd,SAAS,IAAI;YACxD,MAAM,OAAO,AAAC,WAEX,OADD,SAAS,IAAI,EAAC,aAEN,OADP,SAAS,KAAK,EAAC,oBAEd,OADM,SAAS,WAAW,EAAC,cAEzB,OADF,SAAS,MAAM,EAAC,gBAIxB,OAHU,SAAS,QAAQ,EAAC,8BAGP,OAArB,SAAS,WAAW,EAAC,YACf,IAAI;YAEN,MAAM,aAAa,AAAC,sCAAyE,OAApC,mBAAmB,UAAS,UAAiC,OAAzB,mBAAmB;YAEhH,oBAAoB;YACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;YAEvB,uCAAuC;YACvC,WAAW;gBACT,gBAAgB;gBAChB,gBAAgB;gBAEhB,2BAA2B;gBAC3B,WAAW;oBACT,YAAY;wBACV,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,QAAQ;wBACR,UAAU;wBACV,aAAa;oBACf;oBACA,gBAAgB;gBAClB,GAAG;YACL,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IAEA,IAAI,iBAAiB,WAAW;QAC9B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU,sHAAA,CAAA,mBAAgB;YAC1B,WAAU;;8BAEV,6LAAC,8NAAA,CAAA,cAAW;oBAAC,MAAM;oBAAI,WAAU;;;;;;8BACjC,6LAAC;oBAAG,WAAU;8BAA6D;;;;;;8BAG3E,6LAAC;oBAAE,WAAU;;wBAAsB;sCAGjC,6LAAC;4BAAE,MAAK;4BAA6B,WAAU;sCAA2C;;;;;;;;;;;;;;;;;;IAMlG;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;QACV,UAAU,sHAAA,CAAA,mBAAgB;QAC1B,UAAU;QACV,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA2C;;;;;;0CAG3E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU;gCACV,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA2C;;;;;;0CAG5E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA2C;;;;;;0CAGlF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,QAAQ;gCACR,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAmB;;;;;;kDACjC,6LAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,6LAAC;wCAAO,OAAM;kDAAgB;;;;;;kDAC9B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI1B,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAA2C;;;;;;0CAG7E,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAK9B,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAW,WAAU;kCAA2C;;;;;;kCAG/E,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,QAAQ;wBACxB,UAAU;wBACV,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,6LAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,6LAAC;gCAAO,OAAM;0CAAqB;;;;;;0CACnC,6LAAC;gCAAO,OAAM;0CAAsB;;;;;;0CACpC,6LAAC;gCAAO,OAAM;0CAAU;;;;;;;;;;;;;;;;;;0BAI5B,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAA2C;;;;;;kCAGlF,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,QAAQ;wBACR,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;0BAM1B,6LAAC,qIAAA,CAAA,UAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,6BACC;;sCACE,6LAAC;4BAAI,WAAU;;;;;;wBAA4E;;iDAI7F;;sCACE,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAS;;;;;;;;;;;;;;AAO/C;GA5OwB;KAAA", "debugId": null}}]}