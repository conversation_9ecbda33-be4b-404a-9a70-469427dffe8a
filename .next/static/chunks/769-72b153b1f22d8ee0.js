"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[769],{556:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},760:(t,e,i)=>{i.d(e,{N:()=>v});var r=i(5155),n=i(2115),s=i(869),o=i(2885),a=i(7494),l=i(845),u=i(7351),h=i(1508);class d extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(t){let{children:e,isPresent:i,anchorX:s,root:o}=t,a=(0,n.useId)(),l=(0,n.useRef)(null),u=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:n,right:h}=u.current;if(i||!l.current||!t||!e)return;l.current.dataset.motionPopId=a;let d=document.createElement("style");c&&(d.nonce=c);let p=null!=o?o:document.head;return p.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(n):"right: ".concat(h),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(d),p.contains(d)&&p.removeChild(d)}},[i]),(0,r.jsx)(d,{isPresent:i,childRef:l,sizeRef:u,children:n.cloneElement(e,{ref:l})})}let p=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:d,anchorX:p,root:f}=t,g=(0,o.M)(m),y=(0,n.useId)(),v=!0,b=(0,n.useMemo)(()=>(v=!1,{id:y,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(g.set(t,!0),g.values()))if(!e)return;a&&a()},register:t=>(g.set(t,!1),()=>g.delete(t))}),[s,g,a]);return h&&v&&(b={...b}),(0,n.useMemo)(()=>{g.forEach((t,e)=>g.set(e,!1))},[s]),n.useEffect(()=>{s||g.size||!a||a()},[s]),"popLayout"===d&&(e=(0,r.jsx)(c,{isPresent:s,anchorX:p,root:f,children:e})),(0,r.jsx)(l.t.Provider,{value:b,children:e})};function m(){return new Map}var f=i(2082);let g=t=>t.key||"";function y(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:d="sync",propagate:c=!1,anchorX:m="left",root:v}=t,[b,x]=(0,f.xQ)(c),w=(0,n.useMemo)(()=>y(e),[e]),k=c&&!b?[]:w.map(g),P=(0,n.useRef)(!0),T=(0,n.useRef)(w),S=(0,o.M)(()=>new Map),[A,M]=(0,n.useState)(w),[E,C]=(0,n.useState)(w);(0,a.E)(()=>{P.current=!1,T.current=w;for(let t=0;t<E.length;t++){let e=g(E[t]);k.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[E,k.length,k.join("-")]);let V=[];if(w!==A){let t=[...w];for(let e=0;e<E.length;e++){let i=E[e],r=g(i);k.includes(r)||(t.splice(e,0,i),V.push(i))}return"wait"===d&&V.length&&(t=V),C(y(t)),M(w),null}let{forceRender:D}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:E.map(t=>{let e=g(t),n=(!c||!!b)&&(w===E||k.includes(e));return(0,r.jsx)(p,{isPresent:n,initial:(!P.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:d,root:v,onExitComplete:n?void 0:()=>{if(!S.has(e))return;S.set(e,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(null==D||D(),C(T.current),c&&(null==x||x()),u&&u())},anchorX:m,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>r});let r=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>r});let r=(0,i(2115).createContext)({})},1508:(t,e,i)=>{i.d(e,{Q:()=>r});let r=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var r=i(2115),n=i(845);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2596:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}(t))&&(r&&(r+=" "),r+=e);return r}},2605:(t,e,i)=>{let r;i.d(e,{P:()=>sp});var n=i(2115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=t=>180*t/Math.PI,l=t=>h(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,r;if(!t||"none"===t)return m(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=p,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=u,r=e}if(!r)return m(e);let s=i[e],o=r[1].split(",").map(g);return"function"==typeof s?s(o):o[s]}function g(t){return parseFloat(t.trim())}let y=t=>e=>"string"==typeof e&&e.startsWith(t),v=y("--"),b=y("var(--"),x=t=>!!b(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}let P=(t,e,i)=>t+(e-t)*i;function T(t){return void 0===t||1===t}function S({scale:t,scaleX:e,scaleY:i}){return!T(t)||!T(e)||!T(i)}function A(t){return S(t)||M(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function M(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function E(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function C(t,e=0,i=1,r,n){t.min=E(t.min,e,i,r,n),t.max=E(t.max,e,i,r,n)}function V(t,{x:e,y:i}){C(t.x,e.translate,e.scale,e.originPoint),C(t.y,i.translate,i.scale,i.originPoint)}function D(t,e){t.min=t.min+e,t.max=t.max+e}function j(t,e,i,r,n=.5){let s=P(t.min,t.max,n);C(t,e,i,s,r)}function R(t,e){j(t.x,e.x,e.scaleX,e.scale,e.originX),j(t.y,e.y,e.scaleY,e.scale,e.originY)}function L(t,e){return k(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let F=new Set(["width","height","top","left","right","bottom",...s]),O=(t,e,i)=>i>e?e:i<t?t:i,B={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},I={...B,transform:t=>O(0,1,t)},z={...B,default:1},U=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),N=U("deg"),$=U("%"),W=U("px"),_=U("vh"),Y=U("vw"),X={...$,parse:t=>$.parse(t)/100,transform:t=>$.transform(100*t)},K=t=>e=>e.test(t),H=[B,W,$,N,Y,_,{test:t=>"auto"===t,parse:t=>t}],G=t=>H.find(K(t)),q=()=>{},Z=()=>{},Q=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),J=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===B||t===W,te=new Set(["x","y","z"]),ti=s.filter(t=>!te.has(t)),tr={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tr.translateX=tr.x,tr.translateY=tr.y;let tn=t=>t,ts={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=to.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:p,postRender:m}=o,f=()=>{let s=ts.useManualTiming?n.timestamp:performance.now();i=!1,ts.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),p.process(n),m.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(f))};return{schedule:to.reduce((e,s)=>{let a=o[s];return e[s]=(e,s=!1,o=!1)=>(!i&&(i=!0,r=!0,n.isProcessing||t(f)),a.schedule(e,s,o)),e},{}),cancel:t=>{for(let e=0;e<to.length;e++)o[to[e]].cancel(t)},state:n,steps:o}}let{schedule:tu,cancel:th,state:td,steps:tc}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tn,!0),tp=new Set,tm=!1,tf=!1,tg=!1;function ty(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ti.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(tg)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tb{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,tu.read(tv),tu.resolveKeyframes(ty))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tx=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tP=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tT=(t,e)=>i=>!!("string"==typeof i&&tP.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tS=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(tk);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tA={...B,transform:t=>Math.round(O(0,255,t))},tM={test:tT("rgb","red"),parse:tS("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tA.transform(t)+", "+tA.transform(e)+", "+tA.transform(i)+", "+tw(I.transform(r))+")"},tE={test:tT("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tM.transform},tC={test:tT("hsl","hue"),parse:tS("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+$.transform(tw(e))+", "+$.transform(tw(i))+", "+tw(I.transform(r))+")"},tV={test:t=>tM.test(t)||tE.test(t)||tC.test(t),parse:t=>tM.test(t)?tM.parse(t):tC.test(t)?tC.parse(t):tE.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tM.transform(t):tC.transform(t),getAnimatableNone:t=>{let e=tV.parse(t);return e.alpha=0,tV.transform(e)}},tD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tj="number",tR="color",tL=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tF(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tL,t=>(tV.test(t)?(r.color.push(s),n.push(tR),i.push(tV.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tj),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tO(t){return tF(t).values}function tB(t){let{split:e,types:i}=tF(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tj?n+=tw(t[s]):e===tR?n+=tV.transform(t[s]):n+=t[s]}return n}}let tI=t=>"number"==typeof t?0:tV.test(t)?tV.getAnimatableNone(t):t,tz={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tk)?.length||0)+(t.match(tD)?.length||0)>0},parse:tO,createTransformer:tB,getAnimatableNone:function(t){let e=tO(t);return tB(t)(e.map(tI))}},tU=new Set(["brightness","contrast","saturate","opacity"]);function tN(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tk)||[];if(!r)return t;let n=i.replace(r,""),s=+!!tU.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let t$=/\b([a-z-]*)\(.*?\)/gu,tW={...tz,getAnimatableNone:t=>{let e=t.match(t$);return e?e.map(tN).join(" "):t}},t_={...B,transform:Math.round},tY={borderWidth:W,borderTopWidth:W,borderRightWidth:W,borderBottomWidth:W,borderLeftWidth:W,borderRadius:W,radius:W,borderTopLeftRadius:W,borderTopRightRadius:W,borderBottomRightRadius:W,borderBottomLeftRadius:W,width:W,maxWidth:W,height:W,maxHeight:W,top:W,right:W,bottom:W,left:W,padding:W,paddingTop:W,paddingRight:W,paddingBottom:W,paddingLeft:W,margin:W,marginTop:W,marginRight:W,marginBottom:W,marginLeft:W,backgroundPositionX:W,backgroundPositionY:W,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:z,scaleX:z,scaleY:z,scaleZ:z,skew:N,skewX:N,skewY:N,distance:W,translateX:W,translateY:W,translateZ:W,x:W,y:W,z:W,perspective:W,transformPerspective:W,opacity:I,originX:X,originY:X,originZ:W,zIndex:t_,fillOpacity:I,strokeOpacity:I,numOctaves:t_},tX={...tY,color:tV,backgroundColor:tV,outlineColor:tV,fill:tV,stroke:tV,borderColor:tV,borderTopColor:tV,borderRightColor:tV,borderBottomColor:tV,borderLeftColor:tV,filter:tW,WebkitFilter:tW},tK=t=>tX[t];function tH(t,e){let i=tK(t);return i!==tW&&(i=tz),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tG=new Set(["auto","none","0"]);class tq extends tb{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&x(r=r.trim())){let n=function t(e,i,r=1){Z(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,s]=function(t){let e=J.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return Q(t)?parseFloat(t):t}return x(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==t.length)return;let[r,n]=t,s=G(r),o=G(n);if(s!==o)if(tt(s)&&tt(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tx(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!tG.has(e)&&tF(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=tH(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=tr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tQ(){r=void 0}let tJ={now:()=>(void 0===r&&tJ.set(td.isProcessing||ts.useManualTiming?td.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tQ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=tJ.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tJ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let i=this.events[t].add(e);return"change"===t?()=>{i(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tJ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t9(t,e){return new t3(t,e)}let t6=[...H,tV,tz],{schedule:t4}=tl(queueMicrotask,!1),t8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t8)t7[t]={isEnabled:e=>t8[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),ei=()=>({min:0,max:0}),er=()=>({x:ei(),y:ei()});var en=i(8972);let es={current:null},eo={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eu(t){return"string"==typeof t||Array.isArray(t)}let eh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ed=["initial",...eh];function ec(t){return el(t.animate)||ed.some(e=>eu(t[e]))}function ep(t){return!!(ec(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ef(t,e,i,r){if("function"==typeof e){let[n,s]=em(r);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=em(r);e=e(void 0!==i?i:t.custom,n,s)}return e}let eg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ey{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tJ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=ec(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tZ(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eo.current||function(){if(eo.current=!0,en.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>es.current=t.matches;t.addEventListener("change",e),e()}else es.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||es.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),th(this.notifyUpdate),th(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=o.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):er()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eg.length;e++){let i=eg[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(tZ(n))t.addValue(r,n);else if(tZ(s))t.addValue(r,t9(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,t9(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t9(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(Q(i)||tx(i)))i=parseFloat(i);else{let r;r=i,!t6.find(K(r))&&tz.test(e)&&(i=tH(t,e))}this.setBaseTarget(t,tZ(i)?i.get():i)}return tZ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=ef(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tZ(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t4.render(this.render)}}class ev extends ey{constructor(){super(...arguments),this.KeyframeResolver=tq}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eb=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ex={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=s.length;function ek(t,e,i){let{style:r,vars:n,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(o.has(t)){l=!0;continue}if(v(t)){n[t]=i;continue}{let e=eb(i,tY[t]);t.startsWith("origin")?(u=!0,a[t]=e):r[t]=e}}if(!e.transform&&(l||i?r.transform=function(t,e,i){let r="",n=!0;for(let o=0;o<ew;o++){let a=s[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=eb(l,tY[a]);if(!u){n=!1;let e=ex[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin=`${t} ${e} ${i}`}}function eP(t,{style:e,vars:i},r,n){let s,o=t.style;for(s in e)o[s]=e[s];for(s in n?.applyProjectionStyles(o,r),i)o.setProperty(s,i[s])}let eT={};function eS(t,{layout:e,layoutId:i}){return o.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eT[t]||"opacity"===t)}function eA(t,e,i){let{style:r}=t,n={};for(let s in r)(tZ(r[s])||e.style&&tZ(e.style[s])||eS(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}class eM extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=eP}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)})(t,e);{let i=window.getComputedStyle(t),r=(v(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return L(t,e)}build(t,e,i){ek(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eA(t,e,i)}}let eE=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eC={offset:"stroke-dashoffset",array:"stroke-dasharray"},eV={offset:"strokeDashoffset",array:"strokeDasharray"};function eD(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(ek(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?eC:eV;t[s.offset]=W.transform(-r);let o=W.transform(e),a=W.transform(i);t[s.array]=`${o} ${a}`}(d,n,s,o,!1)}let ej=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eR=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eL(t,e,i){let r=eA(t,e,i);for(let i in t)(tZ(t[i])||tZ(e[i]))&&(r[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}class eF extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=er}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tK(e);return t&&t.default||0}return e=ej.has(e)?e:eE(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eL(t,e,i)}build(t,e,i){eD(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in eP(t,e,void 0,r),e.attrs)t.setAttribute(ej.has(i)?i:eE(i),e.attrs[i])}mount(t){this.isSVGTag=eR(t.tagName),super.mount(t)}}let eO=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eB(t){if("string"!=typeof t||t.includes("-"));else if(eO.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eI=i(5155),ez=i(869);let eU=(0,n.createContext)({strict:!1});var eN=i(1508);let e$=(0,n.createContext)({});function eW(t){return Array.isArray(t)?t.join(" "):t}let e_=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eY(t,e,i){for(let r in e)tZ(e[r])||eS(r,i)||(t[r]=e[r])}let eX=()=>({...e_(),attrs:{}}),eK=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eH(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eK.has(t)}let eG=t=>!eH(t);try{!function(t){"function"==typeof t&&(eG=e=>e.startsWith("on")?!eH(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eq=i(845),eZ=i(2885);function eQ(t){return tZ(t)?t.get():t}let eJ=t=>(e,i)=>{let r=(0,n.useContext)(e$),s=(0,n.useContext)(eq.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,n){return{latestValues:function(t,e,i,r){let n={},s=r(t,{});for(let t in s)n[t]=eQ(s[t]);let{initial:o,animate:a}=t,l=ec(t),u=ep(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!el(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let r=ef(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,r,n,t),renderState:e()}})(t,e,r,s);return i?o():(0,eZ.M)(o)},e0=eJ({scrapeMotionValuesFromProps:eA,createRenderState:e_}),e1=eJ({scrapeMotionValuesFromProps:eL,createRenderState:eX}),e2=Symbol.for("motionComponentSymbol");function e5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e3="data-"+eE("framerAppearId"),e9=(0,n.createContext)({});var e6=i(7494);function e4(t){var e,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(s);let a=eB(t)?e1:e0;function l(e,i){var s;let l,u={...(0,n.useContext)(eN.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,n.useContext)(ez.L).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(ec(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eu(e)?e:void 0,animate:eu(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(e$));return(0,n.useMemo)(()=>({initial:e,animate:i}),[eW(e),eW(i)])}(e),c=a(e,h);if(!h&&en.B){(0,n.useContext)(eU).strict;let e=function(t){let{drag:e,layout:i}=t7;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=e.MeasureLayout,d.visualElement=function(t,e,i,r,s){let{visualElement:o}=(0,n.useContext)(e$),a=(0,n.useContext)(eU),l=(0,n.useContext)(eq.t),u=(0,n.useContext)(eN.Q).reducedMotion,h=(0,n.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,n.useContext)(e9);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&e5(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,c);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e3],f=(0,n.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return(0,e6.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,n.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),d}(t,c,u,o,e.ProjectionNode)}return(0,eI.jsxs)(e$.Provider,{value:d,children:[l&&d.visualElement?(0,eI.jsx)(l,{visualElement:d.visualElement,...u}):null,function(t,e,i,{latestValues:r},s,o=!1){let a=(eB(t)?function(t,e,i,r){let s=(0,n.useMemo)(()=>{let i=eX();return eD(i,e,eR(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eY(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return eY(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let i=e_();return ek(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(e,r,s,t),l=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(eG(n)||!0===i&&eH(n)||!e&&!eH(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(e,"string"==typeof t,o),u=t!==n.Fragment?{...l,...a,ref:i}:{},{children:h}=e,d=(0,n.useMemo)(()=>tZ(h)?h.get():h,[h]);return(0,n.createElement)(t,{...u,children:d})}(t,e,(s=d.visualElement,(0,n.useCallback)(t=>{t&&c.onMount&&c.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):e5(i)&&(i.current=t))},[s])),c,h,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let u=(0,n.forwardRef)(l);return u[e2]=t,u}function e8(t,e,i){let r=t.getProps();return ef(r,e,void 0!==i?i:r.custom,t)}function e7(t,e){return t?.[e]??t?.default??t}let it=t=>Array.isArray(t);function ie(t,e){let i=t.getValue("willChange");if(tZ(i)&&i.add)return i.add(e);if(!i&&ts.WillChange){let i=new ts.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let ii=(t,e)=>i=>e(t(i)),ir=(...t)=>t.reduce(ii),is=t=>1e3*t,io={layout:0,mainThread:0,waapi:0};function ia(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function il(t,e){return i=>i>0?e:t}let iu=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},ih=[tE,tM,tC];function id(t){let e=ih.find(e=>e.test(t));if(q(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tC&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=ia(a,r,t+1/3),s=ia(a,r,t),o=ia(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let ic=(t,e)=>{let i=id(t),r=id(e);if(!i||!r)return il(t,e);let n={...i};return t=>(n.red=iu(i.red,r.red,t),n.green=iu(i.green,r.green,t),n.blue=iu(i.blue,r.blue,t),n.alpha=P(i.alpha,r.alpha,t),tM.transform(n))},ip=new Set(["none","hidden"]);function im(t,e){return i=>P(t,e,i)}function ig(t){return"number"==typeof t?im:"string"==typeof t?x(t)?il:tV.test(t)?ic:ib:Array.isArray(t)?iy:"object"==typeof t?tV.test(t)?ic:iv:il}function iy(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>ig(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function iv(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=ig(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let ib=(t,e)=>{let i=tz.createTransformer(e),r=tF(t),n=tF(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ip.has(t)&&!n.values.length||ip.has(e)&&!r.values.length?function(t,e){return ip.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):ir(iy(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):(q(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),il(t,e))};function ix(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?P(t,e,i):ig(t)(t,e)}let iw=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>th(e),now:()=>td.isProcessing?td.timestamp:tJ.now()}},ik=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function iP(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iT(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let iS={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iA(t,e){return t*Math.sqrt(1-e*e)}let iM=["duration","bounce"],iE=["stiffness","damping","mass"];function iC(t,e){return e.some(e=>void 0!==t[e])}function iV(t=iS.visualDuration,e=iS.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iS.velocity,stiffness:iS.stiffness,damping:iS.damping,mass:iS.mass,isResolvedFromDuration:!1,...t};if(!iC(t,iE)&&iC(t,iM))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:iS.mass,stiffness:r,damping:n}}else{let i=function({duration:t=iS.duration,bounce:e=iS.bounce,velocity:i=iS.velocity,mass:r=iS.mass}){let n,s;q(t<=is(iS.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=O(iS.minDamping,iS.maxDamping,o),t=O(iS.minDuration,iS.maxDuration,t/1e3),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/iA(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=iA(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=is(t),isNaN(a))return{stiffness:iS.stiffness,damping:iS.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:iS.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-((r.velocity||0)/1e3)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=Math.sqrt(u/d)/1e3,b=5>Math.abs(y);if(n||(n=b?iS.restSpeed.granular:iS.restSpeed.default),s||(s=b?iS.restDelta.granular:iS.restDelta.default),g<1){let t=iA(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let x={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let r=0===t?f:0;g<1&&(r=0===t?is(f):iT(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(iP(x),2e4),e=ik(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function iD({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,g=p+f,y=void 0===o?g:o(g);y!==g&&(f=y-p);let v=t=>-f*Math.exp(-t/r),b=t=>y+v(t),x=t=>{let e=v(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?y:i},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;d=t,c=iV({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:iT(b,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,x(t),w(t)),void 0!==d&&t>=d)?c.next(t-d):(e||x(t),m)}}}iV.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(iP(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:n/1e3}}(t,100,iV);return t.ease=e.ease,t.duration=is(e.duration),t.type="keyframes",t};let ij=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function iR(t,e,i,r){return t===e&&i===r?tn:n=>0===n||1===n?n:ij(function(t,e,i,r,n){let s,o,a=0;do(s=ij(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o}(n,0,1,t,i),e,r)}let iL=iR(.42,0,1,1),iF=iR(0,0,.58,1),iO=iR(.42,0,.58,1),iB=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iI=t=>e=>1-t(1-e),iz=iR(.33,1.53,.69,.99),iU=iI(iz),iN=iB(iU),i$=t=>(t*=2)<1?.5*iU(t):.5*(2-Math.pow(2,-10*(t-1))),iW=t=>1-Math.sin(Math.acos(t)),i_=iI(iW),iY=iB(iW),iX=t=>Array.isArray(t)&&"number"==typeof t[0],iK={linear:tn,easeIn:iL,easeInOut:iO,easeOut:iF,circIn:iW,circInOut:iY,circOut:i_,backIn:iU,backInOut:iN,backOut:iz,anticipate:i$},iH=t=>{if(iX(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,r,n]=t;return iR(e,i,r,n)}return"string"==typeof t?(Z(void 0!==iK[t],`Invalid easing type '${t}'`,"invalid-easing-type"),iK[t]):t},iG=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function iq({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=Array.isArray(r)&&"number"!=typeof r[0]?r.map(iH):iH(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(Z(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||ts.mix||ix,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=ir(Array.isArray(e)?e[i]||tn:e,s)),r.push(s)}return r}(e,r,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=iG(t[r],t[r+1],i);return a[r](n)};return i?e=>u(O(t[0],t[s-1],e)):u}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=iG(0,e,r);t.push(P(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||iO).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let iZ=t=>null!==t;function iQ(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(iZ),o=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let iJ={decay:iD,inertia:iD,tween:iq,keyframes:iq,spring:iV};function i0(t){"string"==typeof t.type&&(t.type=iJ[t.type])}class i1{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let i2=t=>t/100;class i5 extends i1{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tJ.now()&&this.tick(tJ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},io.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i0(t);let{type:e=iq,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:o}=t,a=e||iq;a!==iq&&"number"!=typeof o[0]&&(this.mixKeyframes=ir(i2,ix(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=iP(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(b=s)),v=O(0,1,i)*o}let x=y?{done:!1,value:u[0]}:b.next(v);n&&(x.value=n(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==iD&&(x.value=iQ(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=is(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tJ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=iw,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tJ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,io.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function i3(t){let e;return()=>(void 0===e&&(e=t()),e)}let i9=i3(()=>void 0!==window.ScrollTimeline),i6={},i4=function(t,e){let i=i3(t);return()=>i6[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),i8=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,i7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i8([0,.65,.55,1]),circOut:i8([.55,0,1,.45]),backIn:i8([.31,.01,.66,-.59]),backOut:i8([.33,1.53,.69,.99])};function rt(t){return"function"==typeof t&&"applyToOptions"in t}class re extends i1{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return rt(t)&&i4()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?i4()?ik(e,i):"ease-out":iX(e)?i8(e):Array.isArray(e)?e.map(e=>t(e,i)||i7.easeOut):i7[e]}(a,n);Array.isArray(d)&&(h.easing=d),ta.value&&io.waapi++;let c={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ta.value&&p.finished.finally(()=>{io.waapi--}),p}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=iQ(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=is(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&i9())?(this.animation.timeline=t,tn):e(this)}}let ri={anticipate:i$,backInOut:iN,circInOut:iY};class rr extends re{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in ri&&(t.ease=ri[t.ease])}(t),i0(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new i5({...s,autoplay:!1}),a=is(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let rn=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tz.test(t)||"0"===t)&&!t.startsWith("url(")),rs=new Set(["opacity","clipPath","filter","transform"]),ro=i3(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ra extends i1{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tJ.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||tb;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=tJ.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=rn(n,e),a=rn(s,e);return q(o===a,`You are trying to animate ${e} from "${n}" to "${s}". "${o?s:n}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||rt(i))&&r)}(t,n,s,o)&&((ts.instantAnimations||!a)&&u?.(iQ(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ro()&&i&&rs.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(h)?new rr({...h,element:h.motionValue.owner.current}):new i5(h);d.finished.then(()=>this.notifyFinished()).catch(tn),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tg=!0,tv(),ty(),tg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rl=t=>null!==t,ru={type:"spring",stiffness:500,damping:25,restSpeed:10},rh={type:"keyframes",duration:.8},rd={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(t,e,i,r={},n,s)=>a=>{let l=e7(r,t)||{},u=l.delay||r.delay||0,{elapsed:h=0}=r;h-=is(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?rh:o.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ru:rd)(t,d)),d.duration&&(d.duration=is(d.duration)),d.repeatDelay&&(d.repeatDelay=is(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(c=!0)),(ts.instantAnimations||ts.skipAnimations)&&(c=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(rl),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(d.keyframes,l);if(void 0!==t)return void tu.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new i5(d):new ra(d)};function rp(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;r&&(s=r);let l=[],u=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let r=t.getValue(e,t.latestValues[e]??null),n=a[e];if(void 0===n||u&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(u,e))continue;let o={delay:i,...e7(s||{},e)},h=r.get();if(void 0!==h&&!r.isAnimating&&!Array.isArray(n)&&n===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e3];if(i){let t=window.MotionHandoffAnimation(i,e,tu);null!==t&&(o.startTime=t,d=!0)}}ie(t,e),r.start(rc(e,r,n,t.shouldReduceMotion&&F.has(e)?{type:!1}:o,t,d));let c=r.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{tu.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=e8(t,e)||{};for(let e in n={...n,...i}){var s;let i=it(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t9(i))}}(t,o)})}),l}function rm(t,e,i={}){let r=e8(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(rp(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=0,s=1,o){let a=[],l=t.variantChildren.size,u=(l-1)*n,h="function"==typeof r,d=h?t=>r(t,l):1===s?(t=0)=>t*n:(t=0)=>u-t*n;return Array.from(t.variantChildren).sort(rf).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(rm(t,e,{...o,delay:i+(h?0:r)+d(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,s,o,a,i)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function rf(t,e){return t.sortNodePosition(e)}function rg(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}let ry=ed.length,rv=[...eh].reverse(),rb=eh.length;function rx(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rw(){return{animate:rx(!0),whileInView:rx(),whileHover:rx(),whileTap:rx(),whileDrag:rx(),whileFocus:rx(),exit:rx()}}class rk{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rP extends rk{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>rm(t,e,i)));else if("string"==typeof e)r=rm(t,e,i);else{let n="function"==typeof e?e8(t,e,i.custom):e;r=Promise.all(rp(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=rw(),r=!0,n=e=>(i,r)=>{let n=e8(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ry;t++){let r=ed[t],n=e.props[r];(eu(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<rb;e++){var c,p;let m=rv[e],f=i[m],g=void 0!==o[m]?o[m]:a[m],y=eu(g),v=m===s?f.isActive:null;!1===v&&(d=e);let b=g===a[m]&&g!==o[m]&&y;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...h},!f.isActive&&null===v||!g&&!f.prevProp||el(g)||"boolean"==typeof g)continue;let x=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!rg(p,c)),w=x||m===s&&f.isActive&&!b&&y||e>d&&y,k=!1,P=Array.isArray(g)?g:[g],T=P.reduce(n(m),{});!1===v&&(T={});let{prevResolvedValues:S={}}=f,A={...S,...T},M=e=>{w=!0,u.has(e)&&(k=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=T[t],i=S[t];if(!h.hasOwnProperty(t))(it(e)&&it(i)?rg(e,i):e===i)?void 0!==e&&u.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=T,f.isActive&&(h={...h,...T}),r&&t.blockInitialAnimation&&(w=!1);let E=!(b&&x)||k;w&&E&&l.push(...P.map(t=>({animation:t,options:{type:m}})))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=e8(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),l.push({animation:e})}let m=!!l.length;return r&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=s(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=rw(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rT=0;class rS extends rk{constructor(){super(...arguments),this.id=rT++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let rA={x:!1,y:!1};function rM(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let rE=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function rC(t){return{point:{x:t.pageX,y:t.pageY}}}function rV(t,e,i,r){return rM(t,e,t=>rE(t)&&i(t,rC(t)),r)}function rD(t){return t.max-t.min}function rj(t,e,i,r=.5){t.origin=r,t.originPoint=P(e.min,e.max,t.origin),t.scale=rD(i)/rD(e),t.translate=P(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rR(t,e,i,r){rj(t.x,e.x,i.x,r?r.originX:void 0),rj(t.y,e.y,i.y,r?r.originY:void 0)}function rL(t,e,i){t.min=i.min+e.min,t.max=t.min+rD(e)}function rF(t,e,i){t.min=e.min-i.min,t.max=t.min+rD(e)}function rO(t,e,i){rF(t.x,e.x,i.x),rF(t.y,e.y,i.y)}function rB(t){return[t("x"),t("y")]}let rI=({current:t})=>t?t.ownerDocument.defaultView:null,rz=(t,e)=>Math.abs(t-e);class rU{constructor(t,e,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rW(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(rz(t.x,e.x)**2+rz(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=td;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rN(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rW("pointercancel"===t.type?this.lastMoveEventInfo:rN(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!rE(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=r||window;let o=rN(rC(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=td;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,rW(o,this.history)),this.removeListeners=ir(rV(this.contextWindow,"pointermove",this.handlePointerMove),rV(this.contextWindow,"pointerup",this.handlePointerUp),rV(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),th(this.updatePoint)}}function rN(t,e){return e?{point:e(t.point)}:t}function r$(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rW({point:t},e){return{point:t,delta:r$(t,r_(e)),offset:r$(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=r_(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>is(.1)));)i--;if(!r)return{x:0,y:0};let s=(n.timestamp-r.timestamp)/1e3;if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function r_(t){return t[t.length-1]}function rY(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function rX(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function rK(t,e,i){return{min:rH(t,e),max:rH(t,i)}}function rH(t,e){return"number"==typeof t?t:t[e]||0}let rG=new WeakMap;class rq{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=er(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rU(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rC(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rA[t])return null;else return rA[t]=!0,()=>{rA[t]=!1};return rA.x||rA.y?null:(rA.x=rA.y=!0,()=>{rA.x=rA.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rB(t=>{let e=this.getAxisMotionValue(t).get()||0;if($.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=rD(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&tu.postRender(()=>n(t,e)),ie(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rB(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,distanceThreshold:i,contextWindow:rI(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,r=e||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:s}=r;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&tu.postRender(()=>o(i,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rZ(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?P(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?P(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&e5(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:rY(t.x,i,n),y:rY(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rK(t,"left","right"),y:rK(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&rB(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e5(e))return!1;let r=e.current;Z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=L(t,i),{scroll:n}=e;return n&&(D(r.x,n.offset.x),D(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:rX(t.x,s.x),y:rX(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=k(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rB(o=>{if(!rZ(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ie(this.visualElement,t),i.start(rc(t,i,0,e,this.visualElement,!1))}stopAnimation(){rB(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rB(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){rB(e=>{let{drag:i}=this.getProps();if(!rZ(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-P(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e5(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rB(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=rD(t),n=rD(e);return n>r?i=iG(e.min,e.max-r,t.min):r>n&&(i=iG(t.min,t.max-n,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rB(e=>{if(!rZ(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(P(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;rG.set(this.visualElement,this);let t=rV(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tu.read(e);let n=rM(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rB(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rZ(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rQ extends rk{constructor(t){super(t),this.removeGroupControls=tn,this.removeListeners=tn,this.controls=new rq(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tn}unmount(){this.removeGroupControls(),this.removeListeners()}}let rJ=t=>(e,i)=>{t&&tu.postRender(()=>t(e,i))};class r0 extends rk{constructor(){super(...arguments),this.removePointerDownListener=tn}onPointerDown(t){this.session=new rU(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rI(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rJ(t),onStart:rJ(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&tu.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=rV(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r1=i(2082);let r2={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r5(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let r3={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!W.test(t))return t;else t=parseFloat(t);let i=r5(t,e.target.x),r=r5(t,e.target.y);return`${i}% ${r}%`}},r9=!1;class r6 extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in r8)eT[t]=r8[t],v(t)&&(eT[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),r9&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),r2.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r9=!0,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||tu.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t4.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function r4(t){let[e,i]=(0,r1.xQ)(),r=(0,n.useContext)(ez.L);return(0,eI.jsx)(r6,{...t,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(e9),isPresent:e,safeToRemove:i})}let r8={borderRadius:{...r3,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r3,borderTopRightRadius:r3,borderBottomLeftRadius:r3,borderBottomRightRadius:r3,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tz.parse(t);if(r.length>5)return t;let n=tz.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=P(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var r7=i(6983);function nt(t){return(0,r7.G)(t)&&"ownerSVGElement"in t}let ne=(t,e)=>t.depth-e.depth;class ni{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ne),this.isDirty=!1,this.children.forEach(t)}}let nr=["TopLeft","TopRight","BottomLeft","BottomRight"],nn=nr.length,ns=t=>"string"==typeof t?parseFloat(t):t,no=t=>"number"==typeof t||W.test(t);function na(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nl=nh(0,.5,i_),nu=nh(.5,.95,tn);function nh(t,e,i){return r=>r<t?0:r>e?1:i(iG(t,e,r))}function nd(t,e){t.min=e.min,t.max=e.max}function nc(t,e){nd(t.x,e.x),nd(t.y,e.y)}function np(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nm(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function nf(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if($.test(e)&&(e=parseFloat(e),e=P(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=P(s.min,s.max,r);t===s&&(a-=e),t.min=nm(t.min,e,i,a,n),t.max=nm(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let ng=["x","scaleX","originX"],ny=["y","scaleY","originY"];function nv(t,e,i,r){nf(t.x,e,ng,i?i.x:void 0,r?r.x:void 0),nf(t.y,e,ny,i?i.y:void 0,r?r.y:void 0)}function nb(t){return 0===t.translate&&1===t.scale}function nx(t){return nb(t.x)&&nb(t.y)}function nw(t,e){return t.min===e.min&&t.max===e.max}function nk(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nP(t,e){return nk(t.x,e.x)&&nk(t.y,e.y)}function nT(t){return rD(t.x)/rD(t.y)}function nS(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nA{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nM={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nE=["","X","Y","Z"],nC=0;function nV(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function nD({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=nC++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(nM.nodes=nM.calculatedTargetDeltas=nM.calculatedProjections=0),this.nodes.forEach(nL),this.nodes.forEach(nN),this.nodes.forEach(n$),this.nodes.forEach(nF),ta.addProjectionMetrics&&ta.addProjectionMetrics(nM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ni)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nt(e)&&!(nt(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{r=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==r&&(r=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tJ.now(),r=({timestamp:e})=>{let n=e-i;n>=250&&(th(r),t(n-250))};return tu.setup(r,!0),()=>th(r)}(n,250),r2.hasAnimatedSinceResize&&(r2.hasAnimatedSinceResize=!1,this.nodes.forEach(nU)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nH,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!nP(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e7(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||nU(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),th(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nW),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[e3];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",tu,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nB);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nI);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nz),this.nodes.forEach(nj),this.nodes.forEach(nR)):this.nodes.forEach(nI),this.clearAllSnapshots();let t=tJ.now();td.delta=O(0,1e3/60,t-td.timestamp),td.timestamp=t,td.isProcessing=!0,tc.update.process(td),tc.preRender.process(td),tc.render.process(td),td.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t4.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nO),this.sharedNodes.forEach(n_)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rD(this.snapshot.measuredBox.x)||rD(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=er(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nx(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||A(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nZ((e=r).x),nZ(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return er();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nJ))){let{scroll:t}=this.root;t&&(D(e.x,t.offset.x),D(e.y,t.offset.y))}return e}removeElementScroll(t){let e=er();if(nc(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&nc(e,t),D(e.x,n.offset.x),D(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=er();nc(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&R(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),A(r.latestValues)&&R(i,r.latestValues)}return A(this.latestValues)&&R(i,this.latestValues),i}removeTransform(t){let e=er();nc(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!A(i.latestValues))continue;S(i.latestValues)&&i.updateSnapshot();let r=er();nc(r,i.measurePageBox()),nv(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return A(this.latestValues)&&nv(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==td.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=td.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rO(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=er(),this.targetWithTransforms=er()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,rL(s.x,o.x,a.x),rL(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nc(this.target,this.layout.layoutBox),V(this.target,this.targetDelta)):nc(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rO(this.relativeTargetOrigin,this.target,t.target),nc(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&nM.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||S(this.parent.latestValues)||M(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===td.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;nc(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&R(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,V(t,s)),r&&A(n.latestValues)&&R(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=er());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(np(this.prevProjectionDelta.x,this.projectionDelta.x),np(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rR(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nS(this.projectionDelta.x,this.prevProjectionDelta.x)&&nS(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&nM.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=er(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nK));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(nY(o.x,t.x,r),nY(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;rO(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,nX(p.x,m.x,f.x,g),nX(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,nw(u.x,c.x)&&nw(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=er()),nc(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=P(0,i.opacity??1,nl(r)),t.opacityExit=P(e.opacity??1,0,nu(r))):s&&(t.opacity=P(e.opacity??1,i.opacity??1,r));for(let n=0;n<nn;n++){let s=`border${nr[n]}Radius`,o=na(e,s),a=na(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||no(o)===no(a)?(t[s]=Math.max(P(ns(o),ns(a),r),0),($.test(a)||$.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=P(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(th(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{r2.hasAnimatedSinceResize=!0,io.layout++,this.motionValue||(this.motionValue=t9(0)),this.currentAnimation=function(t,e,i){let r=tZ(t)?t:t9(t);return r.start(rc("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{io.layout--},onComplete:()=>{io.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&nQ(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||er();let e=rD(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=rD(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}nc(e,i),R(e,n),rR(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nA),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&nV("z",t,r,this.animationValues);for(let e=0;e<nE.length;e++)nV(`rotate${nE[e]}`,t,r,this.animationValues),nV(`skew${nE[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eQ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eQ(e?.pointerEvents)||""),this.hasProjected&&!A(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(s=i(n,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,eT){if(void 0===n[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=eT[e],l="none"===s?n[e]:i(n[e],r);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=r===this?eQ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nB),this.root.sharedNodes.clear()}}}function nj(t){t.updateLayout()}function nR(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?rB(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=rD(r);r.min=i[t].min,r.max=r.min+n}):nQ(n,e.layoutBox,i)&&rB(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=rD(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=ee();rR(o,i,e.layoutBox);let a=ee();s?rR(a,t.applyTransform(r,!0),e.measuredBox):rR(a,i,e.layoutBox);let l=!nx(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=er();rO(o,e.layoutBox,n.layoutBox);let a=er();rO(a,i,s.layoutBox),nP(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nL(t){ta.value&&nM.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nF(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nO(t){t.clearSnapshot()}function nB(t){t.clearMeasurements()}function nI(t){t.isLayoutDirty=!1}function nz(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nU(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nN(t){t.resolveTargetDelta()}function n$(t){t.calcProjection()}function nW(t){t.resetSkewAndRotation()}function n_(t){t.removeLeadSnapshot()}function nY(t,e,i){t.translate=P(e.translate,0,i),t.scale=P(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nX(t,e,i,r){t.min=P(e.min,i.min,r),t.max=P(e.max,i.max,r)}function nK(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nH={duration:.45,ease:[.4,0,.1,1]},nG=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nq=nG("applewebkit/")&&!nG("chrome/")?Math.round:tn;function nZ(t){t.min=nq(t.min),t.max=nq(t.max)}function nQ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nT(e)-nT(i)))}function nJ(t){return t!==t.root&&t.scroll?.wasRoot}let n0=nD({attachResizeListener:(t,e)=>rM(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),n1={current:void 0},n2=nD({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!n1.current){let t=new n0({});t.mount(window),t.setOptions({layoutScroll:!0}),n1.current=t}return n1.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n5(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function n3(t){return!("touch"===t.pointerType||rA.x||rA.y)}function n9(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&tu.postRender(()=>n(e,rC(e)))}class n6 extends rk{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=n5(t,i),o=t=>{if(!n3(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{n3(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(n9(this.node,e,"Start"),t=>n9(this.node,t,"End"))))}unmount(){}}class n4 extends rk{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ir(rM(this.node.current,"focus",()=>this.onFocus()),rM(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var n8=i(7351);let n7=(t,e)=>!!e&&(t===e||n7(t,e.parentElement)),st=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),se=new WeakSet;function si(t){return e=>{"Enter"===e.key&&t(e)}}function sr(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function sn(t){return rE(t)&&!(rA.x||rA.y)}function ss(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&tu.postRender(()=>n(e,rC(e)))}class so extends rk{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=n5(t,i),o=t=>{let r=t.currentTarget;if(!sn(t))return;se.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),se.has(r)&&se.delete(r),sn(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||n7(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,n8.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let r=si(()=>{if(se.has(i))return;sr(i,"down");let t=si(()=>{sr(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sr(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)})(t,n)),st.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(ss(this.node,e,"Start"),(t,{success:e})=>ss(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sa=new WeakMap,sl=new WeakMap,su=t=>{let e=sa.get(t.target);e&&e(t)},sh=t=>{t.forEach(su)},sd={some:0,all:1};class sc extends rk{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sd[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;sl.has(i)||sl.set(i,{});let r=sl.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(sh,{root:t,...e})),r[n]}(e);return sa.set(t,i),r.observe(t),()=>{sa.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sp=function(t,e){if("undefined"==typeof Proxy)return e4;let i=new Map,r=(i,r)=>e4(i,r,t,e);return new Proxy((t,e)=>r(t,e),{get:(n,s)=>"create"===s?r:(i.has(s)||i.set(s,e4(s,void 0,t,e)),i.get(s))})}({animation:{Feature:rP},exit:{Feature:rS},inView:{Feature:sc},tap:{Feature:so},focus:{Feature:n4},hover:{Feature:n6},pan:{Feature:r0},drag:{Feature:rQ,ProjectionNode:n2,MeasureLayout:r4},layout:{ProjectionNode:n2,MeasureLayout:r4}},(t,e)=>eB(t)?new eF(e):new eM(e,{allowProjection:t!==n.Fragment}))},2664:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let r=i(9991),n=i(7102);function s(t){if(!(0,r.isAbsoluteUrl)(t))return!0;try{let e=(0,r.getLocationOrigin)(),i=new URL(t,e);return i.origin===e&&(0,n.hasBasePath)(i.pathname)}catch(t){return!1}}},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let r=i(6966)._(i(8859)),n=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||n.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2885:(t,e,i)=>{i.d(e,{M:()=>n});var r=i(2115);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},4416:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5684:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},5695:(t,e,i)=>{var r=i(8999);i.o(r,"usePathname")&&i.d(e,{usePathname:function(){return r.usePathname}})},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=i(2115);function n(t,e){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let t=i.current;t&&(i.current=null,t());let e=n.current;e&&(n.current=null,e())}else t&&(i.current=s(t,r)),e&&(n.current=s(e,r))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return g},useLinkStatus:function(){return v}});let r=i(6966),n=i(5155),s=r._(i(2115)),o=i(2757),a=i(5227),l=i(9818),u=i(6654),h=i(9991),d=i(5929);i(3230);let c=i(4930),p=i(2664),m=i(6634);function f(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function g(t){let e,i,r,[o,g]=(0,s.useOptimistic)(c.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:b,as:x,children:w,prefetch:k=null,passHref:P,replace:T,shallow:S,scroll:A,onClick:M,onMouseEnter:E,onTouchStart:C,legacyBehavior:V=!1,onNavigate:D,ref:j,unstable_dynamicOnHover:R,...L}=t;e=w,V&&("string"==typeof e||"number"==typeof e)&&(e=(0,n.jsx)("a",{children:e}));let F=s.default.useContext(a.AppRouterContext),O=!1!==k,B=null===k||"auto"===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:z}=s.default.useMemo(()=>{let t=f(b);return{href:t,as:x?f(x):t}},[b,x]);V&&(i=s.default.Children.only(e));let U=V?i&&"object"==typeof i&&i.ref:j,N=s.default.useCallback(t=>(null!==F&&(v.current=(0,c.mountLinkInstance)(t,I,F,B,O,g)),()=>{v.current&&((0,c.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,c.unmountPrefetchableInstance)(t)}),[O,I,F,B,g]),$={ref:(0,u.useMergedRef)(N,U),onClick(t){V||"function"!=typeof M||M(t),V&&i.props&&"function"==typeof i.props.onClick&&i.props.onClick(t),F&&(t.defaultPrevented||function(t,e,i,r,n,o,a){let{nodeName:l}=t.currentTarget;if(!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){n&&(t.preventDefault(),location.replace(e));return}if(t.preventDefault(),a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}s.default.startTransition(()=>{(0,m.dispatchNavigateAction)(i||e,n?"replace":"push",null==o||o,r.current)})}}(t,I,z,v,T,A,D))},onMouseEnter(t){V||"function"!=typeof E||E(t),V&&i.props&&"function"==typeof i.props.onMouseEnter&&i.props.onMouseEnter(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===R)},onTouchStart:function(t){V||"function"!=typeof C||C(t),V&&i.props&&"function"==typeof i.props.onTouchStart&&i.props.onTouchStart(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===R)}};return(0,h.isAbsoluteUrl)(z)?$.href=z:V&&!P&&("a"!==i.type||"href"in i.props)||($.href=(0,d.addBasePath)(z)),r=V?s.default.cloneElement(i,$):(0,n.jsx)("a",{...L,...$,children:e}),(0,n.jsx)(y.Provider,{value:o,children:r})}i(3180);let y=(0,s.createContext)(c.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(y);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,i)=>{i.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},7351:(t,e,i)=>{i.d(e,{s:()=>n});var r=i(6983);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>n});var r=i(2115);let n=i(8972).B?r.useLayoutEffect:r.useEffect},8859:(t,e)=>{function i(t){let e={};for(let[i,r]of t.entries()){let t=e[i];void 0===t?e[i]=r:Array.isArray(t)?t.push(r):e[i]=[t,r]}return e}function r(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;for(let[i,n]of Object.entries(t))if(Array.isArray(n))for(let t of n)e.append(i,r(t));else e.set(i,r(n));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,r]of e.entries())t.append(i,r)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},8883:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(t,e,i)=>{i.d(e,{B:()=>r});let r="undefined"!=typeof window},9688:(t,e,i)=>{i.d(e,{QP:()=>tt});let r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},n=/^\[(.+)\]$/,s=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:o(e,t)).classGroupId=i;return}if("function"==typeof t)return a(t)?void s(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,n])=>{s(n,o(e,t),i,r)})})},o=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},a=t=>t.isThemeGetter,l=/\s+/;function u(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=h(t))&&(r&&(r+=" "),r+=e);return r}let h=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=h(t[r]))&&(i&&(i+=" "),i+=e);return i},d=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},c=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=t=>m.test(t),w=t=>!!t&&!Number.isNaN(Number(t)),k=t=>!!t&&Number.isInteger(Number(t)),P=t=>t.endsWith("%")&&w(t.slice(0,-1)),T=t=>f.test(t),S=()=>!0,A=t=>g.test(t)&&!y.test(t),M=()=>!1,E=t=>v.test(t),C=t=>b.test(t),V=t=>!j(t)&&!I(t),D=t=>Y(t,G,M),j=t=>c.test(t),R=t=>Y(t,q,A),L=t=>Y(t,Z,w),F=t=>Y(t,K,M),O=t=>Y(t,H,C),B=t=>Y(t,J,E),I=t=>p.test(t),z=t=>X(t,q),U=t=>X(t,Q),N=t=>X(t,K),$=t=>X(t,G),W=t=>X(t,H),_=t=>X(t,J,!0),Y=(t,e,i)=>{let r=c.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},X=(t,e,i=!1)=>{let r=p.exec(t);return!!r&&(r[1]?e(r[1]):i)},K=t=>"position"===t||"percentage"===t,H=t=>"image"===t||"url"===t,G=t=>"length"===t||"size"===t||"bg-size"===t,q=t=>"length"===t,Z=t=>"number"===t,Q=t=>"family-name"===t,J=t=>"shadow"===t;Symbol.toStringTag;let tt=function(t,...e){let i,o,a,h=function(l){let u;return o=(i={cache:(t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}})((u=e.reduce((t,e)=>e(t),t())).cacheSize),parseClassName:(t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i,r=[],n=0,s=0,o=0;for(let i=0;i<t.length;i++){let a=t[i];if(0===n&&0===s){if(":"===a){r.push(t.slice(o,i)),o=i+1;continue}if("/"===a){e=i;continue}}"["===a?n++:"]"===a?n--:"("===a?s++:")"===a&&s--}let a=0===r.length?t:t.substring(o),l=(i=a).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:e&&e>o?e-o:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r})(u),sortModifiers:(t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}})(u),...(t=>{let e=(t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)s(i[t],r,t,e);return r})(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||(t=>{if(n.test(t)){let e=n.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}})(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&o[t]?[...r,...o[t]]:r}}})(u)}).cache.get,a=i.cache.set,h=d,d(l)};function d(t){let e=o(t);if(e)return e;let r=((t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(l),u="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:l,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(l){u=e+(u.length>0?" "+u:u);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){u=e+(u.length>0?" "+u:u);continue}m=!1}let g=s(h).join(":"),y=d?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let b=n(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(y+e)}u=e+(u.length>0?" "+u:u)}return u})(t,i);return a(t,r),r}return function(){return h(u.apply(null,arguments))}}(()=>{let t=d("color"),e=d("font"),i=d("text"),r=d("font-weight"),n=d("tracking"),s=d("leading"),o=d("breakpoint"),a=d("container"),l=d("spacing"),u=d("radius"),h=d("shadow"),c=d("inset-shadow"),p=d("text-shadow"),m=d("drop-shadow"),f=d("blur"),g=d("perspective"),y=d("aspect"),v=d("ease"),b=d("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...M(),I,j],C=()=>["auto","hidden","clip","visible","scroll"],Y=()=>["auto","contain","none"],X=()=>[I,j,l],K=()=>[x,"full","auto",...X()],H=()=>[k,"none","subgrid",I,j],G=()=>["auto",{span:["full",k,I,j]},k,I,j],q=()=>[k,"auto",I,j],Z=()=>["auto","min","max","fr",I,j],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...X()],te=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...X()],ti=()=>[t,I,j],tr=()=>[...M(),N,F,{position:[I,j]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",$,D,{size:[I,j]}],to=()=>[P,z,R],ta=()=>["","none","full",u,I,j],tl=()=>["",w,z,R],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[w,P,N,F],tc=()=>["","none",f,I,j],tp=()=>["none",w,I,j],tm=()=>["none",w,I,j],tf=()=>[w,I,j],tg=()=>[x,"full",...X()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[T],breakpoint:[T],color:[S],container:[T],"drop-shadow":[T],ease:["in","out","in-out"],font:[V],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[T],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[T],shadow:[T],spacing:["px",w],text:[T],"text-shadow":[T],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,j,I,y]}],container:["container"],columns:[{columns:[w,j,I,a]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:Y()}],"overscroll-x":[{"overscroll-x":Y()}],"overscroll-y":[{"overscroll-y":Y()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:K()}],"inset-x":[{"inset-x":K()}],"inset-y":[{"inset-y":K()}],start:[{start:K()}],end:[{end:K()}],top:[{top:K()}],right:[{right:K()}],bottom:[{bottom:K()}],left:[{left:K()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",I,j]}],basis:[{basis:[x,"full","auto",a,...X()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",j]}],grow:[{grow:["",w,I,j]}],shrink:[{shrink:["",w,I,j]}],order:[{order:[k,"first","last","none",I,j]}],"grid-cols":[{"grid-cols":H()}],"col-start-end":[{col:G()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":H()}],"row-start-end":[{row:G()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:X()}],"gap-x":[{"gap-x":X()}],"gap-y":[{"gap-y":X()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:X()}],px:[{px:X()}],py:[{py:X()}],ps:[{ps:X()}],pe:[{pe:X()}],pt:[{pt:X()}],pr:[{pr:X()}],pb:[{pb:X()}],pl:[{pl:X()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":X()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":X()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,z,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,I,L]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",P,j]}],"font-family":[{font:[U,j,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,I,j]}],"line-clamp":[{"line-clamp":[w,"none",I,L]}],leading:[{leading:[s,...X()]}],"list-image":[{"list-image":["none",I,j]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",I,j]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",I,R]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[w,"auto",I,j]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:X()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I,j]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I,j]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,I,j],radial:["",I,j],conic:[k,I,j]},W,O]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,I,j]}],"outline-w":[{outline:["",w,z,R]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,_,B]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",c,_,B]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[w,R]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",p,_,B]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[w,I,j]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[I,j]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",I,j]}],filter:[{filter:["","none",I,j]}],blur:[{blur:tc()}],brightness:[{brightness:[w,I,j]}],contrast:[{contrast:[w,I,j]}],"drop-shadow":[{"drop-shadow":["","none",m,_,B]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",w,I,j]}],"hue-rotate":[{"hue-rotate":[w,I,j]}],invert:[{invert:["",w,I,j]}],saturate:[{saturate:[w,I,j]}],sepia:[{sepia:["",w,I,j]}],"backdrop-filter":[{"backdrop-filter":["","none",I,j]}],"backdrop-blur":[{"backdrop-blur":tc()}],"backdrop-brightness":[{"backdrop-brightness":[w,I,j]}],"backdrop-contrast":[{"backdrop-contrast":[w,I,j]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,I,j]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,I,j]}],"backdrop-invert":[{"backdrop-invert":["",w,I,j]}],"backdrop-opacity":[{"backdrop-opacity":[w,I,j]}],"backdrop-saturate":[{"backdrop-saturate":[w,I,j]}],"backdrop-sepia":[{"backdrop-sepia":["",w,I,j]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":X()}],"border-spacing-x":[{"border-spacing-x":X()}],"border-spacing-y":[{"border-spacing-y":X()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",I,j]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",I,j]}],ease:[{ease:["linear","initial",v,I,j]}],delay:[{delay:[w,I,j]}],animate:[{animate:["none",b,I,j]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,I,j]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[I,j,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I,j]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":X()}],"scroll-mx":[{"scroll-mx":X()}],"scroll-my":[{"scroll-my":X()}],"scroll-ms":[{"scroll-ms":X()}],"scroll-me":[{"scroll-me":X()}],"scroll-mt":[{"scroll-mt":X()}],"scroll-mr":[{"scroll-mr":X()}],"scroll-mb":[{"scroll-mb":X()}],"scroll-ml":[{"scroll-ml":X()}],"scroll-p":[{"scroll-p":X()}],"scroll-px":[{"scroll-px":X()}],"scroll-py":[{"scroll-py":X()}],"scroll-ps":[{"scroll-ps":X()}],"scroll-pe":[{"scroll-pe":X()}],"scroll-pt":[{"scroll-pt":X()}],"scroll-pr":[{"scroll-pr":X()}],"scroll-pb":[{"scroll-pb":X()}],"scroll-pl":[{"scroll-pl":X()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I,j]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[w,z,R,L]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9946:(t,e,i)=>{i.d(e,{A:()=>l});var r=i(2115);let n=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},s=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:i="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:d,...c}=t;return(0,r.createElement)("svg",{ref:e,...o,width:n,height:n,stroke:i,strokeWidth:l?24*Number(a)/Number(n):a,className:s("lucide",u),...!h&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(c)&&{"aria-hidden":"true"},...c},[...d.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,r.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,r.createElement)(a,{ref:o,iconNode:e,className:s("lucide-".concat(n(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...u})});return i.displayName=n(t),i}},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return b}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(t){let e,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>n.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let r=await t.getInitialProps(e);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);