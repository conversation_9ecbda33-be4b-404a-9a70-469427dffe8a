{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Section.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Section.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Section.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Section.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Grid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Grid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Grid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Grid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/ShopCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/ShopCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/ShopCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/ShopCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/ShopCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/ShopCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/app/shops/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Layout from '@/components/layout/Layout'\nimport Section from '@/components/ui/Section'\nimport Grid from '@/components/ui/Grid'\nimport ShopCard from '@/components/ui/ShopCard'\nimport Button from '@/components/ui/Button'\nimport { getAllShops, generatePageMeta } from '@/lib/data'\nimport { Store, Shield, Truck, Heart } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  ...generatePageMeta('shops'),\n}\n\nexport default function ShopsPage() {\n  const shops = getAllShops()\n\n  return (\n    <Layout>\n      {/* Hero Section */}\n      <Section\n        title=\"Official Femmepod Shops\"\n        subtitle=\"Where to Buy\"\n        className=\"bg-gradient-to-br from-background to-surface\"\n      >\n        <div className=\"max-w-3xl mx-auto text-center\">\n          <p className=\"text-xl text-text-secondary leading-relaxed mb-8\">\n            Find all official Femmepod merchandise through our trusted partners. \n            Each platform offers unique products and shipping options to serve you better, \n            no matter where you are in the world.\n          </p>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3\">\n                <Shield size={24} className=\"text-background\" />\n              </div>\n              <span className=\"text-sm font-medium text-text-primary\">Quality Guaranteed</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3\">\n                <Truck size={24} className=\"text-background\" />\n              </div>\n              <span className=\"text-sm font-medium text-text-primary\">Worldwide Shipping</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3\">\n                <Store size={24} className=\"text-background\" />\n              </div>\n              <span className=\"text-sm font-medium text-text-primary\">Multiple Platforms</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-accent-primary rounded-full flex items-center justify-center mb-3\">\n                <Heart size={24} className=\"text-background\" />\n              </div>\n              <span className=\"text-sm font-medium text-text-primary\">Artist Supported</span>\n            </div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Shops Grid */}\n      <Section\n        title=\"Our Partner Stores\"\n        subtitle=\"Choose Your Platform\"\n      >\n        <Grid columns={{ sm: 1, md: 2, lg: 2 }}>\n          {shops.map((shop) => (\n            <ShopCard key={shop.id} shop={shop} />\n          ))}\n        </Grid>\n      </Section>\n\n      {/* Regional Information */}\n      <Section\n        title=\"Regional Availability\"\n        subtitle=\"Shipping & Support\"\n        className=\"bg-surface\"\n      >\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          <div className=\"bg-background border border-border p-6\">\n            <h3 className=\"font-heading text-xl font-semibold text-text-primary mb-4\">\n              🇺🇸 United States & International\n            </h3>\n            <ul className=\"space-y-2 text-text-secondary\">\n              <li>• <strong>Redbubble:</strong> Worldwide shipping, extensive product range</li>\n              <li>• <strong>Threadless:</strong> Premium quality, US/EU focus</li>\n              <li>• <strong>Mini Store:</strong> Curated selection, global shipping</li>\n            </ul>\n          </div>\n          \n          <div className=\"bg-background border border-border p-6\">\n            <h3 className=\"font-heading text-xl font-semibold text-text-primary mb-4\">\n              🇮🇳 India & South Asia\n            </h3>\n            <ul className=\"space-y-2 text-text-secondary\">\n              <li>• <strong>Frankly Wearing:</strong> Local production, faster delivery</li>\n              <li>• <strong>Redbubble:</strong> International option with local fulfillment</li>\n              <li>• <strong>Mini Store:</strong> Global shipping available</li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"mt-8 p-6 bg-accent-primary/10 border border-accent-primary/20 rounded\">\n          <h4 className=\"font-heading text-lg font-semibold text-text-primary mb-3\">\n            💡 Pro Tip: Choose the Right Platform\n          </h4>\n          <p className=\"text-text-secondary\">\n            Each platform offers different products, shipping speeds, and pricing. \n            Redbubble has the widest selection, Threadless focuses on premium apparel, \n            Frankly Wearing is perfect for India, and Mini Store offers curated collections. \n            Check multiple platforms to find the best option for your needs!\n          </p>\n        </div>\n      </Section>\n\n      {/* Quality Guarantee */}\n      <Section\n        title=\"Our Quality Promise\"\n        subtitle=\"Vector Art Guarantee\"\n        className=\"bg-gradient-to-br from-surface to-background\"\n      >\n        <div className=\"max-w-3xl mx-auto text-center\">\n          <p className=\"text-xl text-text-secondary mb-8 leading-relaxed\">\n            Every Femmepod design is created as high-resolution vector art, ensuring \n            crisp, clear prints at any size. Our partners use premium materials and \n            printing techniques to bring your favorite designs to life.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button href=\"/quality-and-partners\" variant=\"primary\" size=\"lg\">\n              Learn About Quality\n            </Button>\n            <Button href=\"/designs\" variant=\"secondary\" size=\"lg\">\n              Browse Designs\n            </Button>\n          </div>\n        </div>\n      </Section>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,GAAG,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;AAC9B;AAEe,SAAS;IACtB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAExB,qBACE,8OAAC,sIAAA,CAAA,UAAM;;0BAEL,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAmD;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE7B,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE7B,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE7B,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;0BAET,cAAA,8OAAC,gIAAA,CAAA,UAAI;oBAAC,SAAS;wBAAE,IAAI;wBAAG,IAAI;wBAAG,IAAI;oBAAE;8BAClC,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,oIAAA,CAAA,UAAQ;4BAAe,MAAM;2BAAf,KAAK,EAAE;;;;;;;;;;;;;;;0BAM5B,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;gBACT,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAmB;;;;;;;0DACjC,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAoB;;;;;;;0DAClC,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAoB;;;;;;;;;;;;;;;;;;;0CAItC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;0DACvC,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAmB;;;;;;;0DACjC,8OAAC;;oDAAG;kEAAE,8OAAC;kEAAO;;;;;;oDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;0BAUvC,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAmD;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAwB,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGjE,8OAAC,kIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAW,SAAQ;oCAAY,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlE", "debugId": null}}]}