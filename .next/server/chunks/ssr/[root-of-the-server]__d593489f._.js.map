{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X } from 'lucide-react'\n\nconst navItems = [\n  { name: 'Home', href: '/' },\n  { name: 'Designs', href: '/designs' },\n  { name: 'Shops', href: '/shops' },\n  { name: 'Quality & Partners', href: '/quality-and-partners' },\n  { name: 'Custom Commissions', href: '/custom-commissions' },\n  { name: 'Community', href: '/community' },\n]\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [scrolled, setScrolled] = useState(false)\n  const pathname = usePathname()\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  useEffect(() => {\n    setIsOpen(false)\n  }, [pathname])\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled ? 'bg-background/95 backdrop-blur-md border-b border-border' : 'bg-transparent'\n      }`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      <nav className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"group\">\n            <motion.div\n              className=\"font-heading text-2xl font-bold text-text-primary group-hover:text-accent-primary transition-colors\"\n              whileHover={{ scale: 1.05 }}\n            >\n              Femmepod\n            </motion.div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative text-sm font-medium uppercase tracking-wider transition-colors hover:text-accent-primary ${\n                  pathname === item.href ? 'text-accent-primary' : 'text-text-primary'\n                }`}\n              >\n                {item.name}\n                {pathname === item.href && (\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-accent-primary\"\n                    layoutId=\"activeTab\"\n                  />\n                )}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden text-text-primary hover:text-accent-primary transition-colors\"\n            onClick={() => setIsOpen(!isOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-b border-border\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"container mx-auto px-4 py-6\">\n                <div className=\"flex flex-col space-y-4\">\n                  {navItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`text-sm font-medium uppercase tracking-wider transition-colors hover:text-accent-primary ${\n                        pathname === item.href ? 'text-accent-primary' : 'text-text-primary'\n                      }`}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n    </motion.header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAsB,MAAM;IAAwB;IAC5D;QAAE,MAAM;QAAsB,MAAM;IAAsB;IAC1D;QAAE,MAAM;QAAa,MAAM;IAAa;CACzC;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;IACZ,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,4DAA4D,EACtE,WAAW,6DAA6D,kBACxE;QACF,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;0CAC3B;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,kGAAkG,EAC5G,aAAa,KAAK,IAAI,GAAG,wBAAwB,qBACjD;;wCAED,KAAK,IAAI;wCACT,aAAa,KAAK,IAAI,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAS;;;;;;;mCAVR,KAAK,IAAI;;;;;;;;;;sCAkBpB,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,cAAW;sCAEV,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAK5C,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,yFAAyF,EACnG,aAAa,KAAK,IAAI,GAAG,wBAAwB,qBACjD;kDAED,KAAK,IAAI;uCANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBpC", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/lib/data.ts"], "sourcesContent": ["import { Design, Shop, Testimonial, SiteConfig } from './types'\n\n// Import JSON data\nimport designsData from '@/data/designs.json'\nimport shopsData from '@/data/shops.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport siteConfigData from '@/data/site-config.json'\n\n// Type the imported data\nconst designs = designsData as Design[]\nconst shops = shopsData as Shop[]\nconst testimonials = testimonialsData as Testimonial[]\nconst siteConfig = siteConfigData as SiteConfig\n\n// Design-related functions\nexport function getAllDesigns(): Design[] {\n  return designs.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())\n}\n\nexport function getFeaturedDesigns(): Design[] {\n  return designs.filter(design => design.featured).slice(0, 6)\n}\n\nexport function getDesignBySlug(slug: string): Design | undefined {\n  return designs.find(design => design.slug === slug)\n}\n\nexport function getDesignsByTheme(theme: string): Design[] {\n  return designs.filter(design => design.themes.includes(theme.toLowerCase()))\n}\n\nexport function getAllThemes(): string[] {\n  const allThemes = designs.flatMap(design => design.themes)\n  return [...new Set(allThemes)].sort()\n}\n\nexport function getRelatedDesigns(currentDesign: Design, limit: number = 3): Design[] {\n  const related = designs\n    .filter(design => \n      design.id !== currentDesign.id && \n      design.themes.some(theme => currentDesign.themes.includes(theme))\n    )\n    .slice(0, limit)\n  \n  // If we don't have enough related designs, fill with other designs\n  if (related.length < limit) {\n    const additional = designs\n      .filter(design => \n        design.id !== currentDesign.id && \n        !related.some(r => r.id === design.id)\n      )\n      .slice(0, limit - related.length)\n    \n    return [...related, ...additional]\n  }\n  \n  return related\n}\n\n// Shop-related functions\nexport function getAllShops(): Shop[] {\n  return shops\n}\n\nexport function getFeaturedShops(): Shop[] {\n  return shops.filter(shop => shop.featured)\n}\n\nexport function getShopById(id: string): Shop | undefined {\n  return shops.find(shop => shop.id === id)\n}\n\nexport function getShopsByRegion(region: string): Shop[] {\n  return shops.filter(shop => shop.regions.includes(region))\n}\n\n// Testimonial-related functions\nexport function getAllTestimonials(): Testimonial[] {\n  return testimonials\n}\n\nexport function getRandomTestimonials(count: number = 3): Testimonial[] {\n  const shuffled = [...testimonials].sort(() => 0.5 - Math.random())\n  return shuffled.slice(0, count)\n}\n\n// Site configuration\nexport function getSiteConfig(): SiteConfig {\n  return siteConfig\n}\n\n// SEO and metadata functions\nexport function generateDesignMeta(design: Design) {\n  return {\n    title: `${design.name} | Femmepod Design Portfolio`,\n    description: `View the \"${design.name}\" artwork by Femmepod. ${design.description}`,\n    keywords: [design.name, 'Femmepod', ...design.themes, 'art', 'design', 'illustration'],\n    ogImage: design.image,\n  }\n}\n\nexport function generatePageMeta(page: string) {\n  const baseMeta = {\n    'home': {\n      title: 'Femmepod: Official Portfolio of Artist Jia',\n      description: 'The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.',\n      keywords: ['Femmepod', 'Jia artist', 'designer portfolio', 'anime art', 'feminist art'],\n    },\n    'designs': {\n      title: 'Design Gallery | Femmepod Portfolio',\n      description: 'Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.',\n      keywords: ['Femmepod designs', 'art portfolio', 'illustration gallery', 'anime art'],\n    },\n    'shops': {\n      title: 'Official Femmepod Shops (US & India)',\n      description: 'Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.',\n      keywords: ['Femmepod shops', 'where to buy Femmepod', 'Redbubble', 'Threadless'],\n    },\n    'custom-commissions': {\n      title: 'Custom Illustrations & Design Commissions by Femmepod',\n      description: 'Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.',\n      keywords: ['custom illustration', 'commission artist', 'hire illustrator', 'custom art'],\n    },\n  }\n\n  return baseMeta[page as keyof typeof baseMeta] || baseMeta.home\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;;;;;AAEA,yBAAyB;AACzB,MAAM,UAAU,8FAAA,CAAA,UAAW;AAC3B,MAAM,QAAQ,4FAAA,CAAA,UAAS;AACvB,MAAM,eAAe,mGAAA,CAAA,UAAgB;AACrC,MAAM,aAAa,qGAAA,CAAA,UAAc;AAG1B,SAAS;IACd,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;AAC3G;AAEO,SAAS;IACd,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,KAAK,CAAC,GAAG;AAC5D;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,WAAW;AAC1E;AAEO,SAAS;IACd,MAAM,YAAY,QAAQ,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;IACzD,OAAO;WAAI,IAAI,IAAI;KAAW,CAAC,IAAI;AACrC;AAEO,SAAS,kBAAkB,aAAqB,EAAE,QAAgB,CAAC;IACxE,MAAM,UAAU,QACb,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,cAAc,MAAM,CAAC,QAAQ,CAAC,SAE3D,KAAK,CAAC,GAAG;IAEZ,mEAAmE;IACnE,IAAI,QAAQ,MAAM,GAAG,OAAO;QAC1B,MAAM,aAAa,QAChB,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE,GAEtC,KAAK,CAAC,GAAG,QAAQ,QAAQ,MAAM;QAElC,OAAO;eAAI;eAAY;SAAW;IACpC;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC3C;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC;AACpD;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,sBAAsB,QAAgB,CAAC;IACrD,MAAM,WAAW;WAAI;KAAa,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC/D,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,SAAS;IACd,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc;IAC/C,OAAO;QACL,OAAO,GAAG,OAAO,IAAI,CAAC,4BAA4B,CAAC;QACnD,aAAa,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,uBAAuB,EAAE,OAAO,WAAW,EAAE;QACnF,UAAU;YAAC,OAAO,IAAI;YAAE;eAAe,OAAO,MAAM;YAAE;YAAO;YAAU;SAAe;QACtF,SAAS,OAAO,KAAK;IACvB;AACF;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,WAAW;QACf,QAAQ;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAY;gBAAc;gBAAsB;gBAAa;aAAe;QACzF;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAiB;gBAAwB;aAAY;QACtF;QACA,SAAS;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAkB;gBAAyB;gBAAa;aAAa;QAClF;QACA,sBAAsB;YACpB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAuB;gBAAqB;gBAAoB;aAAa;QAC1F;IACF;IAEA,OAAO,QAAQ,CAAC,KAA8B,IAAI,SAAS,IAAI;AACjE", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { getSiteConfig } from '@/lib/data'\nimport { Instagram, Twitter, Mail } from 'lucide-react'\n\nexport default function Footer() {\n  const siteConfig = getSiteConfig()\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-surface border-t border-border mt-20\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-2\">\n            <Link href=\"/\" className=\"inline-block mb-4\">\n              <h3 className=\"font-heading text-2xl font-bold text-text-primary\">\n                Femmepod\n              </h3>\n            </Link>\n            <p className=\"text-text-secondary mb-6 max-w-md\">\n              {siteConfig.artist.bio}\n            </p>\n            \n            {/* Social Links */}\n            <div className=\"flex space-x-4\">\n              <a\n                href={`mailto:${siteConfig.links.email}`}\n                className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                aria-label=\"Email\"\n              >\n                <Mail size={20} />\n              </a>\n              {siteConfig.links.instagram && (\n                <a\n                  href={siteConfig.links.instagram}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                  aria-label=\"Instagram\"\n                >\n                  <Instagram size={20} />\n                </a>\n              )}\n              {siteConfig.links.twitter && (\n                <a\n                  href={siteConfig.links.twitter}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                  aria-label=\"Twitter\"\n                >\n                  <Twitter size={20} />\n                </a>\n              )}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"font-heading text-lg font-semibold text-text-primary mb-4\">\n              Quick Links\n            </h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/designs\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Design Gallery\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/shops\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Official Shops\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/custom-commissions\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Custom Work\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/community\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Community\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h4 className=\"font-heading text-lg font-semibold text-text-primary mb-4\">\n              Support\n            </h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/quality-and-partners\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Quality Guarantee\n                </Link>\n              </li>\n              <li>\n                <a\n                  href={`mailto:${siteConfig.links.email}`}\n                  className=\"text-text-secondary hover:text-accent-primary transition-colors\"\n                >\n                  Contact Artist\n                </a>\n              </li>\n              <li>\n                <Link href=\"/custom-commissions\" className=\"text-text-secondary hover:text-accent-primary transition-colors\">\n                  Commission Info\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-text-secondary text-sm\">\n            © {currentYear} Femmepod. All rights reserved.\n          </p>\n          \n          <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n            <span className=\"text-text-secondary text-sm\">\n              Made with ❤️ for the modern chimera\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;;;;;;8CAIpE,8OAAC;oCAAE,WAAU;8CACV,WAAW,MAAM,CAAC,GAAG;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAM,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,KAAK,EAAE;4CACxC,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;wCAEb,WAAW,KAAK,CAAC,SAAS,kBACzB,8OAAC;4CACC,MAAM,WAAW,KAAK,CAAC,SAAS;4CAChC,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;wCAGpB,WAAW,KAAK,CAAC,OAAO,kBACvB,8OAAC;4CACC,MAAM,WAAW,KAAK,CAAC,OAAO;4CAC9B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAOvB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAkE;;;;;;;;;;;sDAIpG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAkE;;;;;;;;;;;sDAIlG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAAkE;;;;;;;;;;;sDAI/G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAkE;;;;;;;;;;;;;;;;;;;;;;;sCAQ1G,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAkE;;;;;;;;;;;sDAIjH,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,KAAK,EAAE;gDACxC,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAAkE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASrH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAA8B;gCACtC;gCAAY;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/CustomCursor.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { motion } from 'framer-motion'\n\nexport default function CustomCursor() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n  const [isHovering, setIsHovering] = useState(false)\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY })\n    }\n\n    const handleMouseEnter = () => setIsHovering(true)\n    const handleMouseLeave = () => setIsHovering(false)\n\n    // Add event listeners for mouse movement\n    window.addEventListener('mousemove', updateMousePosition)\n\n    // Add event listeners for hover states on interactive elements\n    const interactiveElements = document.querySelectorAll('a, button, [role=\"button\"]')\n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter)\n      el.addEventListener('mouseleave', handleMouseLeave)\n    })\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition)\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter)\n        el.removeEventListener('mouseleave', handleMouseLeave)\n      })\n    }\n  }, [])\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 w-5 h-5 bg-accent-primary rounded-full pointer-events-none z-[9999] mix-blend-difference\"\n      animate={{\n        x: mousePosition.x - 10,\n        y: mousePosition.y - 10,\n        scale: isHovering ? 2 : 1,\n      }}\n      transition={{\n        type: \"spring\",\n        stiffness: 500,\n        damping: 28,\n        mass: 0.5,\n      }}\n      style={{\n        backgroundColor: isHovering ? 'var(--color-accent-secondary)' : 'var(--color-accent-primary)',\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,MAAM,mBAAmB,IAAM,cAAc;QAC7C,MAAM,mBAAmB,IAAM,cAAc;QAE7C,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,aAAa;QAErC,+DAA+D;QAC/D,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;QACtD,oBAAoB,OAAO,CAAC,CAAA;YAC1B,GAAG,gBAAgB,CAAC,cAAc;YAClC,GAAG,gBAAgB,CAAC,cAAc;QACpC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,GAAG,mBAAmB,CAAC,cAAc;gBACrC,GAAG,mBAAmB,CAAC,cAAc;YACvC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YACP,GAAG,cAAc,CAAC,GAAG;YACrB,GAAG,cAAc,CAAC,GAAG;YACrB,OAAO,aAAa,IAAI;QAC1B;QACA,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;QACA,OAAO;YACL,iBAAiB,aAAa,kCAAkC;QAClE;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport Navigation from './Navigation'\nimport Footer from './Footer'\nimport CustomCursor from '../ui/CustomCursor'\n\ninterface LayoutProps {\n  children: ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background text-text-primary\">\n      <CustomCursor />\n      <Navigation />\n      <main className=\"pt-20\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Utility function for scroll-triggered animations\nexport const fadeInUpVariants = {\n  hidden: {\n    opacity: 0,\n    y: 30,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: 'easeOut',\n    },\n  },\n}\n\n// Utility function for staggered animations\nexport const staggerContainer = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n}\n\n// Utility function for geolocation-based recommendations\nexport const getCountryFromIP = async (): Promise<string> => {\n  try {\n    const response = await fetch('https://ipapi.co/json/')\n    const data = await response.json()\n    return data.country_code || 'US'\n  } catch (error) {\n    console.error('Failed to get country from IP:', error)\n    return 'US' // Default to US\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,mBAAmB;IAC9B,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,KAAK,gBAAgB;;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { staggerContainer, fadeInUpVariants } from '@/lib/utils'\n\ninterface SectionProps {\n  children: ReactNode\n  title?: string\n  subtitle?: string\n  className?: string\n  containerClassName?: string\n  animate?: boolean\n  id?: string\n}\n\nexport default function Section({\n  children,\n  title,\n  subtitle,\n  className = '',\n  containerClassName = '',\n  animate = true,\n  id,\n}: SectionProps) {\n  const content = (\n    <section className={`py-20 ${className}`} id={id}>\n      <div className={`container mx-auto px-4 ${containerClassName}`}>\n        {(title || subtitle) && (\n          <div className=\"text-center mb-16\">\n            {subtitle && (\n              <motion.p\n                variants={animate ? fadeInUpVariants : undefined}\n                className=\"text-accent-primary font-medium uppercase tracking-wider mb-4\"\n              >\n                {subtitle}\n              </motion.p>\n            )}\n            {title && (\n              <motion.h2\n                variants={animate ? fadeInUpVariants : undefined}\n                className=\"font-heading text-h2 font-semibold text-text-primary\"\n              >\n                {title}\n              </motion.h2>\n            )}\n          </div>\n        )}\n        {children}\n      </div>\n    </section>\n  )\n\n  if (animate) {\n    return (\n      <motion.div\n        variants={staggerContainer}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, margin: \"-100px\" }}\n      >\n        {content}\n      </motion.div>\n    )\n  }\n\n  return content\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAgBe,SAAS,QAAQ,EAC9B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,UAAU,IAAI,EACd,EAAE,EACW;IACb,MAAM,wBACJ,8OAAC;QAAQ,WAAW,CAAC,MAAM,EAAE,WAAW;QAAE,IAAI;kBAC5C,cAAA,8OAAC;YAAI,WAAW,CAAC,uBAAuB,EAAE,oBAAoB;;gBAC3D,CAAC,SAAS,QAAQ,mBACjB,8OAAC;oBAAI,WAAU;;wBACZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU,UAAU,mHAAA,CAAA,mBAAgB,GAAG;4BACvC,WAAU;sCAET;;;;;;wBAGJ,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU,UAAU,mHAAA,CAAA,mBAAgB,GAAG;4BACvC,WAAU;sCAET;;;;;;;;;;;;gBAKR;;;;;;;;;;;;IAKP,IAAI,SAAS;QACX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU,mHAAA,CAAA,mBAAgB;YAC1B,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAS;sBAExC;;;;;;IAGP;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { staggerContainer, fadeInUpVariants } from '@/lib/utils'\n\ninterface GridProps {\n  children: ReactNode\n  columns?: {\n    sm?: number\n    md?: number\n    lg?: number\n    xl?: number\n  }\n  gap?: number\n  className?: string\n  animate?: boolean\n}\n\nexport default function Grid({\n  children,\n  columns = { sm: 1, md: 2, lg: 3, xl: 3 },\n  gap = 6,\n  className = '',\n  animate = true,\n}: GridProps) {\n  const gridClasses = `\n    grid\n    grid-cols-${columns.sm || 1}\n    md:grid-cols-${columns.md || 2}\n    lg:grid-cols-${columns.lg || 3}\n    xl:grid-cols-${columns.xl || 3}\n    gap-${gap}\n    ${className}\n  `\n\n  if (animate) {\n    return (\n      <motion.div\n        className={gridClasses}\n        variants={staggerContainer}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, margin: \"-50px\" }}\n      >\n        {children}\n      </motion.div>\n    )\n  }\n\n  return (\n    <div className={gridClasses}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAmBe,SAAS,KAAK,EAC3B,QAAQ,EACR,UAAU;IAAE,IAAI;IAAG,IAAI;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACxC,MAAM,CAAC,EACP,YAAY,EAAE,EACd,UAAU,IAAI,EACJ;IACV,MAAM,cAAc,CAAC;;cAET,EAAE,QAAQ,EAAE,IAAI,EAAE;iBACf,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAClB,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAClB,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC3B,EAAE,IAAI;IACV,EAAE,UAAU;EACd,CAAC;IAED,IAAI,SAAS;QACX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW;YACX,UAAU,mHAAA,CAAA,mBAAgB;YAC1B,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAQ;sBAEvC;;;;;;IAGP;IAEA,qBACE,8OAAC;QAAI,WAAW;kBACb;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/DesignCard.tsx"], "sourcesContent": ["'use client'\n\nimport { Design } from '@/lib/types'\nimport { motion } from 'framer-motion'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { fadeInUpVariants } from '@/lib/utils'\n\ninterface DesignCardProps {\n  design: Design\n  priority?: boolean\n}\n\nexport default function DesignCard({ design, priority = false }: DesignCardProps) {\n  return (\n    <motion.div\n      variants={fadeInUpVariants}\n      whileHover={{ y: -8 }}\n      className=\"group relative overflow-hidden bg-surface border border-border transition-all duration-300 hover:border-accent-primary\"\n    >\n      <Link href={`/designs/${design.slug}`}>\n        <div className=\"relative aspect-square overflow-hidden\">\n          <Image\n            src={design.image}\n            alt={design.name}\n            fill\n            className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n            priority={priority}\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n          />\n          \n          {/* Overlay */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n          \n          {/* Featured badge */}\n          {design.featured && (\n            <div className=\"absolute top-4 right-4 bg-accent-primary text-background px-2 py-1 text-xs font-medium uppercase tracking-wider\">\n              Featured\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-6\">\n          <h3 className=\"font-heading text-xl font-semibold text-text-primary mb-2 group-hover:text-accent-primary transition-colors\">\n            {design.name}\n          </h3>\n          \n          <p className=\"text-text-secondary text-sm mb-4 line-clamp-2\">\n            {design.description}\n          </p>\n          \n          {/* Themes */}\n          <div className=\"flex flex-wrap gap-2 mb-4\">\n            {design.themes.slice(0, 3).map((theme) => (\n              <span\n                key={theme}\n                className=\"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border\"\n              >\n                {theme}\n              </span>\n            ))}\n            {design.themes.length > 3 && (\n              <span className=\"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border\">\n                +{design.themes.length - 3}\n              </span>\n            )}\n          </div>\n\n          {/* Shop count */}\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-text-secondary text-sm\">\n              Available on {design.shopLinks.length} platform{design.shopLinks.length !== 1 ? 's' : ''}\n            </span>\n            \n            <motion.div\n              className=\"text-accent-primary opacity-0 group-hover:opacity-100 transition-opacity\"\n              whileHover={{ x: 4 }}\n            >\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </motion.div>\n          </div>\n        </div>\n      </Link>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAae,SAAS,WAAW,EAAE,MAAM,EAAE,WAAW,KAAK,EAAmB;IAC9E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU,mHAAA,CAAA,mBAAgB;QAC1B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,WAAU;kBAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,SAAS,EAAE,OAAO,IAAI,EAAE;;8BACnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,KAAK;4BACjB,KAAK,OAAO,IAAI;4BAChB,IAAI;4BACJ,WAAU;4BACV,UAAU;4BACV,OAAM;;;;;;sCAIR,8OAAC;4BAAI,WAAU;;;;;;wBAGd,OAAO,QAAQ,kBACd,8OAAC;4BAAI,WAAU;sCAAkH;;;;;;;;;;;;8BAMrI,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,OAAO,IAAI;;;;;;sCAGd,8OAAC;4BAAE,WAAU;sCACV,OAAO,WAAW;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC9B,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8OAAC;oCAAK,WAAU;;wCAAoG;wCAChH,OAAO,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;sCAM/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAA8B;wCAC9B,OAAO,SAAS,CAAC,MAAM;wCAAC;wCAAU,OAAO,SAAS,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;8CAGxF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG;oCAAE;8CAEnB,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAO,OAAM;kDAChE,cAAA,8OAAC;4CAAK,GAAE;4CAA4B,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/H", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx"], "sourcesContent": ["'use client'\n\nimport { ButtonHTMLAttributes, ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary'\n  size?: 'sm' | 'md' | 'lg'\n  children: ReactNode\n  href?: string\n  external?: boolean\n}\n\nexport default function Button({\n  variant = 'primary',\n  size = 'md',\n  children,\n  className,\n  href,\n  external = false,\n  ...props\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center\n    border-0 font-medium uppercase tracking-wider\n    cursor-pointer transition-all duration-200\n    relative overflow-hidden\n  `\n\n  const variants = {\n    primary: `\n      bg-accent-primary text-background\n      hover:bg-accent-secondary hover:-translate-y-0.5\n      hover:shadow-[0_8px_25px_rgba(255,0,122,0.3)]\n    `,\n    secondary: `\n      bg-transparent text-accent-primary border-2 border-accent-primary\n      hover:bg-accent-primary hover:text-background\n    `,\n  }\n\n  const sizes = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg',\n  }\n\n  const classes = cn(\n    baseClasses,\n    variants[variant],\n    sizes[size],\n    className\n  )\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={external ? '_blank' : undefined}\n        rel={external ? 'noopener noreferrer' : undefined}\n        className={classes}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n      >\n        {children}\n      </motion.a>\n    )\n  }\n\n  return (\n    <motion.button\n      className={classes}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      {...props}\n    >\n      {children}\n    </motion.button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAce,SAAS,OAAO,EAC7B,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,WAAW,KAAK,EAChB,GAAG,OACS;IACZ,MAAM,cAAc,CAAC;;;;;EAKrB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;;IAIV,CAAC;QACD,WAAW,CAAC;;;IAGZ,CAAC;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,MAAM;YACN,QAAQ,WAAW,WAAW;YAC9B,KAAK,WAAW,wBAAwB;YACxC,WAAW;YACX,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;sBAEvB;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW;QACX,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACvB,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/app/designs/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo } from 'react'\nimport { Metadata } from 'next'\nimport Layout from '@/components/layout/Layout'\nimport Section from '@/components/ui/Section'\nimport Grid from '@/components/ui/Grid'\nimport DesignCard from '@/components/ui/DesignCard'\nimport Button from '@/components/ui/Button'\nimport { getAllDesigns, getAllThemes, generatePageMeta } from '@/lib/data'\nimport { Search, Filter } from 'lucide-react'\n\nexport default function DesignsPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedTheme, setSelectedTheme] = useState('all')\n  const [sortBy, setSortBy] = useState('newest')\n\n  const allDesigns = getAllDesigns()\n  const allThemes = getAllThemes()\n\n  const filteredAndSortedDesigns = useMemo(() => {\n    let filtered = allDesigns\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(design =>\n        design.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        design.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        design.themes.some(theme => theme.toLowerCase().includes(searchTerm.toLowerCase()))\n      )\n    }\n\n    // Filter by theme\n    if (selectedTheme !== 'all') {\n      filtered = filtered.filter(design =>\n        design.themes.includes(selectedTheme)\n      )\n    }\n\n    // Sort designs\n    switch (sortBy) {\n      case 'newest':\n        return filtered.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())\n      case 'oldest':\n        return filtered.sort((a, b) => new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime())\n      case 'name':\n        return filtered.sort((a, b) => a.name.localeCompare(b.name))\n      default:\n        return filtered\n    }\n  }, [allDesigns, searchTerm, selectedTheme, sortBy])\n\n  return (\n    <Layout>\n      {/* Hero Section */}\n      <Section\n        title=\"Design Gallery\"\n        subtitle=\"Complete Collection\"\n        className=\"bg-gradient-to-br from-background to-surface\"\n      >\n        <div className=\"max-w-2xl mx-auto text-center\">\n          <p className=\"text-xl text-text-secondary leading-relaxed\">\n            Explore the complete collection of Femmepod designs. Each piece tells a unique story \n            of empowerment, strength, and artistic vision.\n          </p>\n        </div>\n      </Section>\n\n      {/* Filters Section */}\n      <Section animate={false} className=\"py-8 bg-surface border-y border-border\">\n        <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n          {/* Search */}\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary\" size={20} />\n            <input\n              type=\"text\"\n              placeholder=\"Search designs...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 bg-background border border-border text-text-primary placeholder-text-secondary focus:border-accent-primary focus:outline-none transition-colors\"\n            />\n          </div>\n\n          {/* Theme Filter */}\n          <div className=\"flex items-center gap-4\">\n            <Filter size={20} className=\"text-text-secondary\" />\n            <select\n              value={selectedTheme}\n              onChange={(e) => setSelectedTheme(e.target.value)}\n              className=\"px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors\"\n            >\n              <option value=\"all\">All Themes</option>\n              {allThemes.map(theme => (\n                <option key={theme} value={theme}>\n                  {theme.charAt(0).toUpperCase() + theme.slice(1)}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Sort */}\n          <div className=\"flex items-center gap-4\">\n            <span className=\"text-text-secondary text-sm\">Sort by:</span>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-4 py-3 bg-background border border-border text-text-primary focus:border-accent-primary focus:outline-none transition-colors\"\n            >\n              <option value=\"newest\">Newest First</option>\n              <option value=\"oldest\">Oldest First</option>\n              <option value=\"name\">Name A-Z</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-text-secondary\">\n            Showing {filteredAndSortedDesigns.length} of {allDesigns.length} designs\n            {selectedTheme !== 'all' && ` in \"${selectedTheme}\"`}\n            {searchTerm && ` matching \"${searchTerm}\"`}\n          </p>\n        </div>\n      </Section>\n\n      {/* Designs Grid */}\n      <Section animate={false}>\n        {filteredAndSortedDesigns.length > 0 ? (\n          <Grid columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}>\n            {filteredAndSortedDesigns.map((design) => (\n              <DesignCard key={design.id} design={design} />\n            ))}\n          </Grid>\n        ) : (\n          <div className=\"text-center py-20\">\n            <div className=\"text-6xl mb-6\">🎨</div>\n            <h3 className=\"font-heading text-2xl font-semibold text-text-primary mb-4\">\n              No designs found\n            </h3>\n            <p className=\"text-text-secondary mb-8\">\n              Try adjusting your search terms or filters to find what you're looking for.\n            </p>\n            <Button\n              onClick={() => {\n                setSearchTerm('')\n                setSelectedTheme('all')\n                setSortBy('newest')\n              }}\n              variant=\"secondary\"\n            >\n              Clear Filters\n            </Button>\n          </div>\n        )}\n      </Section>\n\n      {/* CTA Section */}\n      <Section\n        title=\"Can't Find What You're Looking For?\"\n        subtitle=\"Custom Commissions\"\n        className=\"bg-gradient-to-br from-surface to-background\"\n      >\n        <div className=\"max-w-2xl mx-auto text-center\">\n          <p className=\"text-xl text-text-secondary mb-8 leading-relaxed\">\n            Every design in this gallery started as a vision. Let's bring your unique vision to life \n            with a custom commission that's perfectly tailored to your story.\n          </p>\n          <Button href=\"/custom-commissions\" variant=\"primary\" size=\"lg\">\n            Commission Custom Art\n          </Button>\n        </div>\n      </Section>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,YAAY,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEnF;QAEA,kBAAkB;QAClB,IAAI,kBAAkB,OAAO;YAC3B,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,MAAM,CAAC,QAAQ,CAAC;QAE3B;QAEA,eAAe;QACf,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;YAC5G,KAAK;gBACH,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;YAC5G,KAAK;gBACH,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YAC5D;gBACE,OAAO;QACX;IACF,GAAG;QAAC;QAAY;QAAY;QAAe;KAAO;IAElD,qBACE,8OAAC,sIAAA,CAAA,UAAM;;0BAEL,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA8C;;;;;;;;;;;;;;;;0BAQ/D,8OAAC,mIAAA,CAAA,UAAO;gBAAC,SAAS;gBAAO,WAAU;;kCACjC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAyE,MAAM;;;;;;kDACjG,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,UAAU,GAAG,CAAC,CAAA,sBACb,8OAAC;oDAAmB,OAAO;8DACxB,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;mDADlC;;;;;;;;;;;;;;;;;0CAQnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAsB;gCACxB,yBAAyB,MAAM;gCAAC;gCAAK,WAAW,MAAM;gCAAC;gCAC/D,kBAAkB,SAAS,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;gCACnD,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;;;;;;;;;;;;;;;;;;0BAMhD,8OAAC,mIAAA,CAAA,UAAO;gBAAC,SAAS;0BACf,yBAAyB,MAAM,GAAG,kBACjC,8OAAC,gIAAA,CAAA,UAAI;oBAAC,SAAS;wBAAE,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;oBAAE;8BACzC,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,8OAAC,sIAAA,CAAA,UAAU;4BAAiB,QAAQ;2BAAnB,OAAO,EAAE;;;;;;;;;yCAI9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAG3E,8OAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAGxC,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAS;gCACP,cAAc;gCACd,iBAAiB;gCACjB,UAAU;4BACZ;4BACA,SAAQ;sCACT;;;;;;;;;;;;;;;;;0BAQP,8OAAC,mIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,UAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAmD;;;;;;sCAIhE,8OAAC,kIAAA,CAAA,UAAM;4BAAC,MAAK;4BAAsB,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}]}