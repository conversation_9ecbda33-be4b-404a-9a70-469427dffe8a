{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/lib/data.ts"], "sourcesContent": ["import { Design, Shop, Testimonial, SiteConfig } from './types'\n\n// Import JSON data\nimport designsData from '@/data/designs.json'\nimport shopsData from '@/data/shops.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport siteConfigData from '@/data/site-config.json'\n\n// Type the imported data\nconst designs = designsData as Design[]\nconst shops = shopsData as Shop[]\nconst testimonials = testimonialsData as Testimonial[]\nconst siteConfig = siteConfigData as SiteConfig\n\n// Design-related functions\nexport function getAllDesigns(): Design[] {\n  return designs.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())\n}\n\nexport function getFeaturedDesigns(): Design[] {\n  return designs.filter(design => design.featured).slice(0, 6)\n}\n\nexport function getDesignBySlug(slug: string): Design | undefined {\n  return designs.find(design => design.slug === slug)\n}\n\nexport function getDesignsByTheme(theme: string): Design[] {\n  return designs.filter(design => design.themes.includes(theme.toLowerCase()))\n}\n\nexport function getAllThemes(): string[] {\n  const allThemes = designs.flatMap(design => design.themes)\n  return [...new Set(allThemes)].sort()\n}\n\nexport function getRelatedDesigns(currentDesign: Design, limit: number = 3): Design[] {\n  const related = designs\n    .filter(design => \n      design.id !== currentDesign.id && \n      design.themes.some(theme => currentDesign.themes.includes(theme))\n    )\n    .slice(0, limit)\n  \n  // If we don't have enough related designs, fill with other designs\n  if (related.length < limit) {\n    const additional = designs\n      .filter(design => \n        design.id !== currentDesign.id && \n        !related.some(r => r.id === design.id)\n      )\n      .slice(0, limit - related.length)\n    \n    return [...related, ...additional]\n  }\n  \n  return related\n}\n\n// Shop-related functions\nexport function getAllShops(): Shop[] {\n  return shops\n}\n\nexport function getFeaturedShops(): Shop[] {\n  return shops.filter(shop => shop.featured)\n}\n\nexport function getShopById(id: string): Shop | undefined {\n  return shops.find(shop => shop.id === id)\n}\n\nexport function getShopsByRegion(region: string): Shop[] {\n  return shops.filter(shop => shop.regions.includes(region))\n}\n\n// Testimonial-related functions\nexport function getAllTestimonials(): Testimonial[] {\n  return testimonials\n}\n\nexport function getRandomTestimonials(count: number = 3): Testimonial[] {\n  const shuffled = [...testimonials].sort(() => 0.5 - Math.random())\n  return shuffled.slice(0, count)\n}\n\n// Site configuration\nexport function getSiteConfig(): SiteConfig {\n  return siteConfig\n}\n\n// SEO and metadata functions\nexport function generateDesignMeta(design: Design) {\n  return {\n    title: `${design.name} | Femmepod Design Portfolio`,\n    description: `View the \"${design.name}\" artwork by Femmepod. ${design.description}`,\n    keywords: [design.name, 'Femmepod', ...design.themes, 'art', 'design', 'illustration'],\n    ogImage: design.image,\n  }\n}\n\nexport function generatePageMeta(page: string) {\n  const baseMeta = {\n    'home': {\n      title: 'Femmepod: Official Portfolio of Artist Jia',\n      description: 'The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.',\n      keywords: ['Femmepod', 'Jia artist', 'designer portfolio', 'anime art', 'feminist art'],\n    },\n    'designs': {\n      title: 'Design Gallery | Femmepod Portfolio',\n      description: 'Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.',\n      keywords: ['Femmepod designs', 'art portfolio', 'illustration gallery', 'anime art'],\n    },\n    'shops': {\n      title: 'Official Femmepod Shops (US & India)',\n      description: 'Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.',\n      keywords: ['Femmepod shops', 'where to buy Femmepod', 'Redbubble', 'Threadless'],\n    },\n    'custom-commissions': {\n      title: 'Custom Illustrations & Design Commissions by Femmepod',\n      description: 'Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.',\n      keywords: ['custom illustration', 'commission artist', 'hire illustrator', 'custom art'],\n    },\n  }\n\n  return baseMeta[page as keyof typeof baseMeta] || baseMeta.home\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;;;;;AAEA,yBAAyB;AACzB,MAAM,UAAU,8FAAA,CAAA,UAAW;AAC3B,MAAM,QAAQ,4FAAA,CAAA,UAAS;AACvB,MAAM,eAAe,mGAAA,CAAA,UAAgB;AACrC,MAAM,aAAa,qGAAA,CAAA,UAAc;AAG1B,SAAS;IACd,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;AAC3G;AAEO,SAAS;IACd,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,EAAE,KAAK,CAAC,GAAG;AAC5D;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAEO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,WAAW;AAC1E;AAEO,SAAS;IACd,MAAM,YAAY,QAAQ,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;IACzD,OAAO;WAAI,IAAI,IAAI;KAAW,CAAC,IAAI;AACrC;AAEO,SAAS,kBAAkB,aAAqB,EAAE,QAAgB,CAAC;IACxE,MAAM,UAAU,QACb,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,cAAc,MAAM,CAAC,QAAQ,CAAC,SAE3D,KAAK,CAAC,GAAG;IAEZ,mEAAmE;IACnE,IAAI,QAAQ,MAAM,GAAG,OAAO;QAC1B,MAAM,aAAa,QAChB,MAAM,CAAC,CAAA,SACN,OAAO,EAAE,KAAK,cAAc,EAAE,IAC9B,CAAC,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE,GAEtC,KAAK,CAAC,GAAG,QAAQ,QAAQ,MAAM;QAElC,OAAO;eAAI;eAAY;SAAW;IACpC;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC3C;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC;AACpD;AAGO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,sBAAsB,QAAgB,CAAC;IACrD,MAAM,WAAW;WAAI;KAAa,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC/D,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,SAAS;IACd,OAAO;AACT;AAGO,SAAS,mBAAmB,MAAc;IAC/C,OAAO;QACL,OAAO,GAAG,OAAO,IAAI,CAAC,4BAA4B,CAAC;QACnD,aAAa,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,uBAAuB,EAAE,OAAO,WAAW,EAAE;QACnF,UAAU;YAAC,OAAO,IAAI;YAAE;eAAe,OAAO,MAAM;YAAE;YAAO;YAAU;SAAe;QACtF,SAAS,OAAO,KAAK;IACvB;AACF;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,WAAW;QACf,QAAQ;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAY;gBAAc;gBAAsB;gBAAa;aAAe;QACzF;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAiB;gBAAwB;aAAY;QACtF;QACA,SAAS;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAkB;gBAAyB;gBAAa;aAAa;QAClF;QACA,sBAAsB;YACpB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAuB;gBAAqB;gBAAoB;aAAa;QAC1F;IACF;IAEA,OAAO,QAAQ,CAAC,KAA8B,IAAI,SAAS,IAAI;AACjE", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport \"./globals.css\";\nimport { getSiteConfig } from '@/lib/data'\n\nconst siteConfig = getSiteConfig()\n\nexport const metadata: Metadata = {\n  title: {\n    default: siteConfig.name,\n    template: `%s | ${siteConfig.name}`,\n  },\n  description: siteConfig.description,\n  keywords: ['Femmepod', 'Jia', 'artist', 'designer', 'anime art', 'feminist art', 'illustration'],\n  authors: [{ name: siteConfig.artist.name }],\n  creator: siteConfig.artist.name,\n  metadataBase: new URL(siteConfig.url),\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    url: siteConfig.url,\n    title: siteConfig.name,\n    description: siteConfig.description,\n    siteName: siteConfig.name,\n    images: [\n      {\n        url: siteConfig.ogImage,\n        width: 1200,\n        height: 630,\n        alt: siteConfig.name,\n      },\n    ],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: siteConfig.name,\n    description: siteConfig.description,\n    images: [siteConfig.ogImage],\n    creator: '@femmepod',\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className=\"antialiased\">\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS,WAAW,IAAI;QACxB,UAAU,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE;IACrC;IACA,aAAa,WAAW,WAAW;IACnC,UAAU;QAAC;QAAY;QAAO;QAAU;QAAY;QAAa;QAAgB;KAAe;IAChG,SAAS;QAAC;YAAE,MAAM,WAAW,MAAM,CAAC,IAAI;QAAC;KAAE;IAC3C,SAAS,WAAW,MAAM,CAAC,IAAI;IAC/B,cAAc,IAAI,IAAI,WAAW,GAAG;IACpC,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,WAAW,GAAG;QACnB,OAAO,WAAW,IAAI;QACtB,aAAa,WAAW,WAAW;QACnC,UAAU,WAAW,IAAI;QACzB,QAAQ;YACN;gBACE,KAAK,WAAW,OAAO;gBACvB,OAAO;gBACP,QAAQ;gBACR,KAAK,WAAW,IAAI;YACtB;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO,WAAW,IAAI;QACtB,aAAa,WAAW,WAAW;QACnC,QAAQ;YAAC,WAAW,OAAO;SAAC;QAC5B,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACb;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}