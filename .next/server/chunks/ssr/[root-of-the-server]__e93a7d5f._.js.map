{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Section.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Section.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Section.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Section.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Grid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Grid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Grid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Grid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/DesignCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/DesignCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/DesignCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/DesignCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/DesignCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/DesignCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/TestimonialCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/TestimonialCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/TestimonialCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/TestimonialCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/Button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/components/seo/StructuredData.tsx"], "sourcesContent": ["import { getSiteConfig } from '@/lib/data'\nimport { Design } from '@/lib/types'\n\ninterface StructuredDataProps {\n  type: 'website' | 'person' | 'imageObject' | 'breadcrumbList'\n  design?: Design\n  breadcrumbs?: Array<{ name: string; url: string }>\n}\n\nexport default function StructuredData({ type, design, breadcrumbs }: StructuredDataProps) {\n  const siteConfig = getSiteConfig()\n\n  const generateWebsiteSchema = () => ({\n    '@context': 'https://schema.org',\n    '@type': 'WebSite',\n    name: siteConfig.name,\n    description: siteConfig.description,\n    url: siteConfig.url,\n    author: {\n      '@type': 'Person',\n      name: siteConfig.artist.name,\n      description: siteConfig.artist.bio,\n      url: siteConfig.url,\n      sameAs: [\n        siteConfig.links.instagram,\n        siteConfig.links.twitter,\n      ].filter(Boolean),\n    },\n    potentialAction: {\n      '@type': 'SearchAction',\n      target: {\n        '@type': 'EntryPoint',\n        urlTemplate: `${siteConfig.url}/designs?search={search_term_string}`,\n      },\n      'query-input': 'required name=search_term_string',\n    },\n  })\n\n  const generatePersonSchema = () => ({\n    '@context': 'https://schema.org',\n    '@type': 'Person',\n    name: siteConfig.artist.name,\n    description: siteConfig.artist.bio,\n    url: siteConfig.url,\n    image: siteConfig.ogImage,\n    sameAs: [\n      siteConfig.links.instagram,\n      siteConfig.links.twitter,\n    ].filter(Boolean),\n    jobTitle: 'Digital Artist & Designer',\n    worksFor: {\n      '@type': 'Organization',\n      name: siteConfig.name,\n      url: siteConfig.url,\n    },\n    knowsAbout: [\n      'Digital Art',\n      'Illustration',\n      'Character Design',\n      'Anime Art',\n      'Feminist Art',\n      'Vector Art',\n    ],\n  })\n\n  const generateImageObjectSchema = (design: Design) => ({\n    '@context': 'https://schema.org',\n    '@type': 'ImageObject',\n    name: design.name,\n    description: design.description,\n    url: `${siteConfig.url}${design.image}`,\n    author: {\n      '@type': 'Person',\n      name: siteConfig.artist.name,\n      url: siteConfig.url,\n    },\n    creator: {\n      '@type': 'Person',\n      name: siteConfig.artist.name,\n      url: siteConfig.url,\n    },\n    dateCreated: design.createdAt,\n    keywords: design.themes.join(', '),\n    license: 'All rights reserved',\n    copyrightHolder: {\n      '@type': 'Person',\n      name: siteConfig.artist.name,\n    },\n    contentUrl: `${siteConfig.url}${design.image}`,\n    thumbnailUrl: `${siteConfig.url}${design.image}`,\n  })\n\n  const generateBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({\n    '@context': 'https://schema.org',\n    '@type': 'BreadcrumbList',\n    itemListElement: breadcrumbs.map((crumb, index) => ({\n      '@type': 'ListItem',\n      position: index + 1,\n      name: crumb.name,\n      item: crumb.url,\n    })),\n  })\n\n  let schema = {}\n\n  switch (type) {\n    case 'website':\n      schema = generateWebsiteSchema()\n      break\n    case 'person':\n      schema = generatePersonSchema()\n      break\n    case 'imageObject':\n      if (design) {\n        schema = generateImageObjectSchema(design)\n      }\n      break\n    case 'breadcrumbList':\n      if (breadcrumbs) {\n        schema = generateBreadcrumbSchema(breadcrumbs)\n      }\n      break\n    default:\n      return null\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(schema, null, 2),\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AASe,SAAS,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAuB;IACvF,MAAM,aAAa,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD;IAE/B,MAAM,wBAAwB,IAAM,CAAC;YACnC,YAAY;YACZ,SAAS;YACT,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW;YACnC,KAAK,WAAW,GAAG;YACnB,QAAQ;gBACN,SAAS;gBACT,MAAM,WAAW,MAAM,CAAC,IAAI;gBAC5B,aAAa,WAAW,MAAM,CAAC,GAAG;gBAClC,KAAK,WAAW,GAAG;gBACnB,QAAQ;oBACN,WAAW,KAAK,CAAC,SAAS;oBAC1B,WAAW,KAAK,CAAC,OAAO;iBACzB,CAAC,MAAM,CAAC;YACX;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,WAAW,GAAG,CAAC,oCAAoC,CAAC;gBACtE;gBACA,eAAe;YACjB;QACF,CAAC;IAED,MAAM,uBAAuB,IAAM,CAAC;YAClC,YAAY;YACZ,SAAS;YACT,MAAM,WAAW,MAAM,CAAC,IAAI;YAC5B,aAAa,WAAW,MAAM,CAAC,GAAG;YAClC,KAAK,WAAW,GAAG;YACnB,OAAO,WAAW,OAAO;YACzB,QAAQ;gBACN,WAAW,KAAK,CAAC,SAAS;gBAC1B,WAAW,KAAK,CAAC,OAAO;aACzB,CAAC,MAAM,CAAC;YACT,UAAU;YACV,UAAU;gBACR,SAAS;gBACT,MAAM,WAAW,IAAI;gBACrB,KAAK,WAAW,GAAG;YACrB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH,CAAC;IAED,MAAM,4BAA4B,CAAC,SAAmB,CAAC;YACrD,YAAY;YACZ,SAAS;YACT,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,WAAW;YAC/B,KAAK,GAAG,WAAW,GAAG,GAAG,OAAO,KAAK,EAAE;YACvC,QAAQ;gBACN,SAAS;gBACT,MAAM,WAAW,MAAM,CAAC,IAAI;gBAC5B,KAAK,WAAW,GAAG;YACrB;YACA,SAAS;gBACP,SAAS;gBACT,MAAM,WAAW,MAAM,CAAC,IAAI;gBAC5B,KAAK,WAAW,GAAG;YACrB;YACA,aAAa,OAAO,SAAS;YAC7B,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC;YAC7B,SAAS;YACT,iBAAiB;gBACf,SAAS;gBACT,MAAM,WAAW,MAAM,CAAC,IAAI;YAC9B;YACA,YAAY,GAAG,WAAW,GAAG,GAAG,OAAO,KAAK,EAAE;YAC9C,cAAc,GAAG,WAAW,GAAG,GAAG,OAAO,KAAK,EAAE;QAClD,CAAC;IAED,MAAM,2BAA2B,CAAC,cAAsD,CAAC;YACvF,YAAY;YACZ,SAAS;YACT,iBAAiB,YAAY,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;oBAClD,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,GAAG;gBACjB,CAAC;QACH,CAAC;IAED,IAAI,SAAS,CAAC;IAEd,OAAQ;QACN,KAAK;YACH,SAAS;YACT;QACF,KAAK;YACH,SAAS;YACT;QACF,KAAK;YACH,IAAI,QAAQ;gBACV,SAAS,0BAA0B;YACrC;YACA;QACF,KAAK;YACH,IAAI,aAAa;gBACf,SAAS,yBAAyB;YACpC;YACA;QACF;YACE,OAAO;IACX;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;QACvC;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/personal/femmepod-new/src/app/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Layout from '@/components/layout/Layout'\nimport Hero from '@/components/ui/Hero'\nimport Section from '@/components/ui/Section'\nimport Grid from '@/components/ui/Grid'\nimport DesignCard from '@/components/ui/DesignCard'\nimport TestimonialCard from '@/components/ui/TestimonialCard'\nimport Button from '@/components/ui/Button'\nimport StructuredData from '@/components/seo/StructuredData'\nimport { getFeaturedDesigns, getRandomTestimonials, getSiteConfig, generatePageMeta } from '@/lib/data'\n\nexport const metadata: Metadata = {\n  ...generatePageMeta('home'),\n}\n\nexport default function Home() {\n  const featuredDesigns = getFeaturedDesigns()\n  const testimonials = getRandomTestimonials(3)\n  const siteConfig = getSiteConfig()\n\n  return (\n    <>\n      <StructuredData type=\"website\" />\n      <StructuredData type=\"person\" />\n\n      <Layout>\n        {/* Hero Section */}\n      <Hero\n        title=\"Art for the Modern Chimera\"\n        subtitle=\"Femmepod Portfolio\"\n        description=\"Discover powerful, empowering artwork that blends anime aesthetics with feminist themes and futuristic elements. Each design tells a story of strength, rebellion, and beauty.\"\n        primaryCTA={{\n          text: \"Explore Designs\",\n          href: \"/designs\"\n        }}\n        secondaryCTA={{\n          text: \"Commission Art\",\n          href: \"/custom-commissions\"\n        }}\n      />\n\n      {/* Featured Designs Section */}\n      <Section\n        title=\"Featured Designs\"\n        subtitle=\"Latest Creations\"\n      >\n        <Grid columns={{ sm: 1, md: 2, lg: 3 }}>\n          {featuredDesigns.map((design, index) => (\n            <DesignCard\n              key={design.id}\n              design={design}\n              priority={index < 3}\n            />\n          ))}\n        </Grid>\n\n        <div className=\"text-center mt-12\">\n          <Button href=\"/designs\" variant=\"secondary\" size=\"lg\">\n            View All Designs\n          </Button>\n        </div>\n      </Section>\n\n      {/* Stats Section */}\n      <Section className=\"bg-surface\">\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n          <div>\n            <div className=\"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2\">\n              {siteConfig.stats.customerSatisfaction}\n            </div>\n            <div className=\"text-text-secondary uppercase tracking-wider text-sm\">\n              Customer Satisfaction\n            </div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2\">\n              {siteConfig.stats.fiveStarReviews}\n            </div>\n            <div className=\"text-text-secondary uppercase tracking-wider text-sm\">\n              Five Star Reviews\n            </div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2\">\n              {siteConfig.stats.designsCreated}\n            </div>\n            <div className=\"text-text-secondary uppercase tracking-wider text-sm\">\n              Designs Created\n            </div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2\">\n              {siteConfig.stats.countriesShipped}\n            </div>\n            <div className=\"text-text-secondary uppercase tracking-wider text-sm\">\n              Countries Shipped\n            </div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Testimonials Section */}\n      <Section\n        title=\"What People Say\"\n        subtitle=\"Community Love\"\n      >\n        <Grid columns={{ sm: 1, md: 2, lg: 3 }}>\n          {testimonials.map((testimonial) => (\n            <TestimonialCard key={testimonial.id} testimonial={testimonial} />\n          ))}\n        </Grid>\n      </Section>\n\n      {/* Custom Work CTA Section */}\n      <Section\n        title=\"Ready for Something Unique?\"\n        subtitle=\"Custom Commissions\"\n        className=\"bg-gradient-to-br from-surface to-background\"\n      >\n        <div className=\"max-w-3xl mx-auto text-center\">\n          <p className=\"text-xl text-text-secondary mb-8 leading-relaxed\">\n            Looking for a custom piece that speaks to your soul? I specialize in creating\n            powerful, personalized artwork that captures your vision with the same energy\n            and artistry you see in my portfolio.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button href=\"/custom-commissions\" variant=\"primary\" size=\"lg\">\n              Start Your Commission\n            </Button>\n            <Button href=\"/community\" variant=\"secondary\" size=\"lg\">\n              Join the Community\n            </Button>\n          </div>\n        </div>\n      </Section>\n    </Layout>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,GAAG,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;AAC7B;AAEe,SAAS;IACtB,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,eAAe,CAAA,GAAA,kHAAA,CAAA,wBAAqB,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,kHAAA,CAAA,gBAAa,AAAD;IAE/B,qBACE;;0BACE,8OAAC,2IAAA,CAAA,UAAc;gBAAC,MAAK;;;;;;0BACrB,8OAAC,2IAAA,CAAA,UAAc;gBAAC,MAAK;;;;;;0BAErB,8OAAC,sIAAA,CAAA,UAAM;;kCAEP,8OAAC,gIAAA,CAAA,UAAI;wBACH,OAAM;wBACN,UAAS;wBACT,aAAY;wBACZ,YAAY;4BACV,MAAM;4BACN,MAAM;wBACR;wBACA,cAAc;4BACZ,MAAM;4BACN,MAAM;wBACR;;;;;;kCAIF,8OAAC,mIAAA,CAAA,UAAO;wBACN,OAAM;wBACN,UAAS;;0CAET,8OAAC,gIAAA,CAAA,UAAI;gCAAC,SAAS;oCAAE,IAAI;oCAAG,IAAI;oCAAG,IAAI;gCAAE;0CAClC,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,sIAAA,CAAA,UAAU;wCAET,QAAQ;wCACR,UAAU,QAAQ;uCAFb,OAAO,EAAE;;;;;;;;;;0CAOpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAW,SAAQ;oCAAY,MAAK;8CAAK;;;;;;;;;;;;;;;;;kCAO1D,8OAAC,mIAAA,CAAA,UAAO;wBAAC,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,oBAAoB;;;;;;sDAExC,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;8CAIxE,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,eAAe;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;8CAIxE,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,cAAc;;;;;;sDAElC,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;8CAIxE,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,gBAAgB;;;;;;sDAEpC,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;;;;;;;;;;;;kCAQ5E,8OAAC,mIAAA,CAAA,UAAO;wBACN,OAAM;wBACN,UAAS;kCAET,cAAA,8OAAC,gIAAA,CAAA,UAAI;4BAAC,SAAS;gCAAE,IAAI;gCAAG,IAAI;gCAAG,IAAI;4BAAE;sCAClC,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC,2IAAA,CAAA,UAAe;oCAAsB,aAAa;mCAA7B,YAAY,EAAE;;;;;;;;;;;;;;;kCAM1C,8OAAC,mIAAA,CAAA,UAAO;wBACN,OAAM;wBACN,UAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmD;;;;;;8CAMhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,UAAM;4CAAC,MAAK;4CAAsB,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDAG/D,8OAAC,kIAAA,CAAA,UAAM;4CAAC,MAAK;4CAAa,SAAQ;4CAAY,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE", "debugId": null}}]}