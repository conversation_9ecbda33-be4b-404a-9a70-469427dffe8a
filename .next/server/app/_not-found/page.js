(()=>{var a={};a.id=492,a.ids=[492],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26759:()=>{},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35367:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43631:()=>{},61135:()=>{},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67939:(a,b,c)=>{"use strict";c.d(b,{Ht:()=>p,Gs:()=>q,N0:()=>h,rM:()=>l,ow:()=>m,jY:()=>j,i6:()=>i,M5:()=>n,NS:()=>k,Q2:()=>o});let d=JSON.parse('[{"id":"design-001","name":"Power Feminist Tiger","slug":"power-feminist-tiger","description":"A fierce design combining the raw power of a tiger with symbols of feminist empowerment, featuring bold colors and dynamic composition that speaks to inner strength and rebellion.","themes":["feminist","empowerment","tiger","anime"],"image":"/images/designs/power-feminist-tiger.jpg","featured":true,"createdAt":"2024-01-15","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/power-feminist-tiger","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/power-feminist-tiger","region":"UK","logo":"/images/logos/redbubble.svg"},{"platform":"Threadless","url":"https://threadless.com/shop/femmepod/power-feminist-tiger","region":"US","logo":"/images/logos/threadless.svg"},{"platform":"Frankly Wearing","url":"https://franklywearing.com/femmepod/power-feminist-tiger","region":"India","logo":"/images/logos/frankly-wearing.svg"}]},{"id":"design-002","name":"Japanese Real Dragon","slug":"japanese-real-dragon","description":"A design that blends traditional Japanese mythology with a modern, realistic art style, capturing the power and majesty of the dragon in stunning detail.","themes":["japanese","mythology","dragon","traditional"],"image":"/images/designs/japanese-real-dragon.jpg","featured":true,"createdAt":"2024-01-10","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/japanese-real-dragon","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Threadless","url":"https://threadless.com/shop/femmepod/japanese-real-dragon","region":"US","logo":"/images/logos/threadless.svg"},{"platform":"Mini Store","url":"https://ministore.com/femmepod/japanese-real-dragon","region":"Global","logo":"/images/logos/mini-store.svg"}]},{"id":"design-003","name":"Cyberpunk Goddess","slug":"cyberpunk-goddess","description":"A futuristic goddess design merging cyberpunk aesthetics with divine feminine energy, featuring neon colors and technological elements.","themes":["cyberpunk","goddess","futuristic","neon"],"image":"/images/designs/cyberpunk-goddess.jpg","featured":false,"createdAt":"2024-01-05","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/cyberpunk-goddess","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Frankly Wearing","url":"https://franklywearing.com/femmepod/cyberpunk-goddess","region":"India","logo":"/images/logos/frankly-wearing.svg"}]},{"id":"design-004","name":"Anime Warrior Princess","slug":"anime-warrior-princess","description":"A powerful anime-style warrior princess design showcasing strength, beauty, and determination with intricate armor details and flowing hair.","themes":["anime","warrior","princess","strength"],"image":"/images/designs/anime-warrior-princess.jpg","featured":true,"createdAt":"2023-12-28","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/anime-warrior-princess","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Threadless","url":"https://threadless.com/shop/femmepod/anime-warrior-princess","region":"US","logo":"/images/logos/threadless.svg"},{"platform":"Mini Store","url":"https://ministore.com/femmepod/anime-warrior-princess","region":"Global","logo":"/images/logos/mini-store.svg"}]},{"id":"design-005","name":"Mystical Moon Phases","slug":"mystical-moon-phases","description":"An ethereal design featuring the phases of the moon with mystical elements, perfect for those who connect with lunar energy and celestial beauty.","themes":["mystical","moon","celestial","phases"],"image":"/images/designs/mystical-moon-phases.jpg","featured":false,"createdAt":"2023-12-20","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/mystical-moon-phases","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Frankly Wearing","url":"https://franklywearing.com/femmepod/mystical-moon-phases","region":"India","logo":"/images/logos/frankly-wearing.svg"}]},{"id":"design-006","name":"Retro Synthwave Cat","slug":"retro-synthwave-cat","description":"A nostalgic synthwave-inspired design featuring a cool cat with retro 80s aesthetics, neon grids, and vibrant sunset colors.","themes":["retro","synthwave","cat","80s"],"image":"/images/designs/retro-synthwave-cat.jpg","featured":false,"createdAt":"2023-12-15","shopLinks":[{"platform":"Redbubble","url":"https://redbubble.com/shop/femmepod/retro-synthwave-cat","region":"US","logo":"/images/logos/redbubble.svg"},{"platform":"Threadless","url":"https://threadless.com/shop/femmepod/retro-synthwave-cat","region":"US","logo":"/images/logos/threadless.svg"}]}]'),e=JSON.parse('[{"id":"redbubble","name":"Redbubble","description":"Global marketplace for independent artists with worldwide shipping and high-quality products.","url":"https://redbubble.com/shop/femmepod","logo":"/images/logos/redbubble.svg","regions":["US","UK","EU","AU","CA"],"featured":true},{"id":"threadless","name":"Threadless","description":"Premium quality t-shirts and apparel with a focus on artistic designs and community.","url":"https://threadless.com/shop/femmepod","logo":"/images/logos/threadless.svg","regions":["US","UK","EU"],"featured":true},{"id":"frankly-wearing","name":"Frankly Wearing","description":"India-based print-on-demand service offering affordable, high-quality apparel with local shipping.","url":"https://franklywearing.com/femmepod","logo":"/images/logos/frankly-wearing.svg","regions":["India","South Asia"],"featured":true},{"id":"mini-store","name":"Mini Store","description":"Curated collection of premium designs available globally with fast shipping options.","url":"https://ministore.com/femmepod","logo":"/images/logos/mini-store.svg","regions":["Global"],"featured":true}]'),f=JSON.parse('[{"id":"testimonial-001","name":"Sarah Chen","content":"Absolutely love my Power Feminist Tiger t-shirt! The design is bold, empowering, and the quality is amazing. Femmepod\'s art speaks to my soul.","rating":5,"platform":"Redbubble","location":"San Francisco, CA"},{"id":"testimonial-002","name":"Priya Sharma","content":"The Japanese Dragon design is stunning! The detail and artistry are incredible. Fast shipping to India through Frankly Wearing too.","rating":5,"platform":"Frankly Wearing","location":"Mumbai, India"},{"id":"testimonial-003","name":"Alex Rodriguez","content":"Femmepod\'s designs are unique and powerful. I\'ve bought three shirts now and each one gets compliments everywhere I go. Highly recommend!","rating":5,"platform":"Threadless","location":"Austin, TX"},{"id":"testimonial-004","name":"Emma Thompson","content":"The Cyberpunk Goddess design is exactly what I was looking for. Perfect blend of futuristic and feminine energy. Love supporting independent artists!","rating":5,"platform":"Redbubble","location":"London, UK"},{"id":"testimonial-005","name":"Yuki Tanaka","content":"Beautiful artwork with deep meaning. The Anime Warrior Princess design is my favorite - it represents strength and beauty perfectly.","rating":5,"platform":"Mini Store","location":"Tokyo, Japan"},{"id":"testimonial-006","name":"Maria Garcia","content":"Femmepod\'s art is revolutionary! Each design tells a story and empowers women. The quality is top-notch and shipping was super fast.","rating":5,"platform":"Redbubble","location":"Barcelona, Spain"}]'),g=JSON.parse('{"name":"Femmepod","description":"The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.","url":"https://femmepod.com","ogImage":"/images/og-image.jpg","links":{"email":"<EMAIL>","instagram":"https://instagram.com/femmepod","twitter":"https://twitter.com/femmepod"},"artist":{"name":"Jia","bio":"Digital artist and designer creating powerful, empowering artwork that blends anime aesthetics with feminist themes and futuristic elements. Based in the intersection of art and activism.","location":"Global"},"stats":{"customerSatisfaction":"98%","fiveStarReviews":"700+","designsCreated":"50+","countriesShipped":"25+"}}');function h(){return d.sort((a,b)=>new Date(b.createdAt||"").getTime()-new Date(a.createdAt||"").getTime())}function i(){return d.filter(a=>a.featured).slice(0,6)}function j(a){return d.find(b=>b.slug===a)}function k(a,b=3){let c=d.filter(b=>b.id!==a.id&&b.themes.some(b=>a.themes.includes(b))).slice(0,b);if(c.length<b){let e=d.filter(b=>b.id!==a.id&&!c.some(a=>a.id===b.id)).slice(0,b-c.length);return[...c,...e]}return c}function l(){return e}function m(){return f}function n(a=3){return[...f].sort(()=>.5-Math.random()).slice(0,a)}function o(){return g}function p(a){return{title:`${a.name} | Femmepod Design Portfolio`,description:`View the "${a.name}" artwork by Femmepod. ${a.description}`,keywords:[a.name,"Femmepod",...a.themes,"art","design","illustration"],ogImage:a.image}}function q(a){let b={home:{title:"Femmepod: Official Portfolio of Artist Jia",description:"The official portfolio and art hub for Femmepod (Jia). Discover unique anime, feminist, and sci-fi designs and find where to buy them.",keywords:["Femmepod","Jia artist","designer portfolio","anime art","feminist art"]},designs:{title:"Design Gallery | Femmepod Portfolio",description:"Browse the complete design portfolio of Femmepod. Filter by theme to find unique anime, mythology, and pop culture illustrations.",keywords:["Femmepod designs","art portfolio","illustration gallery","anime art"]},shops:{title:"Official Femmepod Shops (US & India)",description:"Find all the official online stores for Femmepod merchandise, including our partners for US, India, and worldwide shipping.",keywords:["Femmepod shops","where to buy Femmepod","Redbubble","Threadless"]},"custom-commissions":{title:"Custom Illustrations & Design Commissions by Femmepod",description:"Commission a unique piece of art from Femmepod. Specializing in custom anime, fantasy, and character illustrations for personal or commercial use.",keywords:["custom illustration","commission artist","hire illustrator","custom art"]}};return b[a]||b.home}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},75535:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},75939:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/personal/femmepod-new/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=[],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/_not-found/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>f});var d=c(37413);c(61135);let e=(0,c(67939).Q2)(),f={title:{default:e.name,template:`%s | ${e.name}`},description:e.description,keywords:["Femmepod","Jia","artist","designer","anime art","feminist art","illustration"],authors:[{name:e.artist.name}],creator:e.artist.name,metadataBase:new URL(e.url),openGraph:{type:"website",locale:"en_US",url:e.url,title:e.name,description:e.description,siteName:e.name,images:[{url:e.ogImage,width:1200,height:630,alt:e.name}]},twitter:{card:"summary_large_image",title:e.name,description:e.description,images:[e.ogImage],creator:"@femmepod"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"},other:{"theme-color":"#FF007A","color-scheme":"dark"}};function g({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:"antialiased",children:a})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24],()=>b(b.s=75939));module.exports=c})();