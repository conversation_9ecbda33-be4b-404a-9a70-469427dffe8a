(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5188:(a,b,c)=>{Promise.resolve().then(c.bind(c,14143)),Promise.resolve().then(c.bind(c,83853)),Promise.resolve().then(c.bind(c,12685)),Promise.resolve().then(c.bind(c,61589)),Promise.resolve().then(c.bind(c,46589)),Promise.resolve().then(c.bind(c,65904)),Promise.resolve().then(c.bind(c,25046))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12685:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/DesignCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/DesignCard.tsx","default")},14143:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,metadata:()=>n});var d=c(37413),e=c(14143),f=c(46589),g=c(65904),h=c(61589),i=c(12685),j=c(25046),k=c(83853),l=c(26785),m=c(67939);let n={...(0,m.Gs)("home")};function o(){let a=(0,m.i6)(),b=(0,m.M5)(3),c=(0,m.Q2)();return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{type:"website"}),(0,d.jsx)(l.A,{type:"person"}),(0,d.jsxs)(e.default,{children:[(0,d.jsx)(f.default,{title:"Art for the Modern Chimera",subtitle:"Femmepod Portfolio",description:"Discover powerful, empowering artwork that blends anime aesthetics with feminist themes and futuristic elements. Each design tells a story of strength, rebellion, and beauty.",primaryCTA:{text:"Explore Designs",href:"/designs"},secondaryCTA:{text:"Commission Art",href:"/custom-commissions"}}),(0,d.jsxs)(g.default,{title:"Featured Designs",subtitle:"Latest Creations",children:[(0,d.jsx)(h.default,{columns:{sm:1,md:2,lg:3},children:a.map((a,b)=>(0,d.jsx)(i.default,{design:a,priority:b<3},a.id))}),(0,d.jsx)("div",{className:"text-center mt-12",children:(0,d.jsx)(k.default,{href:"/designs",variant:"secondary",size:"lg",children:"View All Designs"})})]}),(0,d.jsx)(g.default,{className:"bg-surface",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2",children:c.stats.customerSatisfaction}),(0,d.jsx)("div",{className:"text-text-secondary uppercase tracking-wider text-sm",children:"Customer Satisfaction"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2",children:c.stats.fiveStarReviews}),(0,d.jsx)("div",{className:"text-text-secondary uppercase tracking-wider text-sm",children:"Five Star Reviews"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2",children:c.stats.designsCreated}),(0,d.jsx)("div",{className:"text-text-secondary uppercase tracking-wider text-sm",children:"Designs Created"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl md:text-4xl font-heading font-bold text-accent-primary mb-2",children:c.stats.countriesShipped}),(0,d.jsx)("div",{className:"text-text-secondary uppercase tracking-wider text-sm",children:"Countries Shipped"})]})]})}),(0,d.jsx)(g.default,{title:"What People Say",subtitle:"Community Love",children:(0,d.jsx)(h.default,{columns:{sm:1,md:2,lg:3},children:b.map(a=>(0,d.jsx)(j.default,{testimonial:a},a.id))})}),(0,d.jsx)(g.default,{title:"Ready for Something Unique?",subtitle:"Custom Commissions",className:"bg-gradient-to-br from-surface to-background",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,d.jsx)("p",{className:"text-xl text-text-secondary mb-8 leading-relaxed",children:"Looking for a custom piece that speaks to your soul? I specialize in creating powerful, personalized artwork that captures your vision with the same energy and artistry you see in my portfolio."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(k.default,{href:"/custom-commissions",variant:"primary",size:"lg",children:"Start Your Commission"}),(0,d.jsx)(k.default,{href:"/community",variant:"secondary",size:"lg",children:"Join the Community"})]})]})})]})]})}},25046:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx","default")},25219:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(60687),e=c(51743),f=c(30474),g=c(85814),h=c.n(g),i=c(4780);function j({design:a,priority:b=!1}){return(0,d.jsx)(e.P.div,{variants:i.HM,whileHover:{y:-8},className:"group relative overflow-hidden bg-surface border border-border transition-all duration-300 hover:border-accent-primary",children:(0,d.jsxs)(h(),{href:`/designs/${a.slug}`,children:[(0,d.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,d.jsx)(f.default,{src:a.image,alt:a.name,fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110",priority:b,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),a.featured&&(0,d.jsx)("div",{className:"absolute top-4 right-4 bg-accent-primary text-background px-2 py-1 text-xs font-medium uppercase tracking-wider",children:"Featured"})]}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-2 group-hover:text-accent-primary transition-colors",children:a.name}),(0,d.jsx)("p",{className:"text-text-secondary text-sm mb-4 line-clamp-2",children:a.description}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[a.themes.slice(0,3).map(a=>(0,d.jsx)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:a},a)),a.themes.length>3&&(0,d.jsxs)("span",{className:"px-2 py-1 bg-background text-text-secondary text-xs uppercase tracking-wider border border-border",children:["+",a.themes.length-3]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-text-secondary text-sm",children:["Available on ",a.shopLinks.length," platform",1!==a.shopLinks.length?"s":""]}),(0,d.jsx)(e.P.div,{className:"text-accent-primary opacity-0 group-hover:opacity-100 transition-opacity",whileHover:{x:4},children:(0,d.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{d:"M7 17L17 7M17 7H7M17 7V17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26785:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(37413),e=c(67939);function f({type:a,design:b,breadcrumbs:c}){let f=(0,e.Q2)(),g={};switch(a){case"website":g={"@context":"https://schema.org","@type":"WebSite",name:f.name,description:f.description,url:f.url,author:{"@type":"Person",name:f.artist.name,description:f.artist.bio,url:f.url,sameAs:[f.links.instagram,f.links.twitter].filter(Boolean)},potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${f.url}/designs?search={search_term_string}`},"query-input":"required name=search_term_string"}};break;case"person":g={"@context":"https://schema.org","@type":"Person",name:f.artist.name,description:f.artist.bio,url:f.url,image:f.ogImage,sameAs:[f.links.instagram,f.links.twitter].filter(Boolean),jobTitle:"Digital Artist & Designer",worksFor:{"@type":"Organization",name:f.name,url:f.url},knowsAbout:["Digital Art","Illustration","Character Design","Anime Art","Feminist Art","Vector Art"]};break;case"imageObject":b&&(g={"@context":"https://schema.org","@type":"ImageObject",name:b.name,description:b.description,url:`${f.url}${b.image}`,author:{"@type":"Person",name:f.artist.name,url:f.url},creator:{"@type":"Person",name:f.artist.name,url:f.url},dateCreated:b.createdAt,keywords:b.themes.join(", "),license:"All rights reserved",copyrightHolder:{"@type":"Person",name:f.artist.name},contentUrl:`${f.url}${b.image}`,thumbnailUrl:`${f.url}${b.image}`});break;case"breadcrumbList":c&&(g={"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:c.map((a,b)=>({"@type":"ListItem",position:b+1,name:a.name,item:a.url}))});break;default:return null}return(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(g,null,2)}})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35404:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21204)),"/Users/<USER>/Documents/personal/femmepod-new/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/personal/femmepod-new/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/personal/femmepod-new/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46589:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Hero.tsx","default")},53807:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(51743),f=c(4780);function g({children:a,columns:b={sm:1,md:2,lg:3,xl:3},gap:c=6,className:g="",animate:h=!0}){let i=`
    grid
    grid-cols-${b.sm||1}
    md:grid-cols-${b.md||2}
    lg:grid-cols-${b.lg||3}
    xl:grid-cols-${b.xl||3}
    gap-${c}
    ${g}
  `;return h?(0,d.jsx)(e.P.div,{className:i,variants:f.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},children:a}):(0,d.jsx)("div",{className:i,children:a})}},58340:(a,b,c)=>{Promise.resolve().then(c.bind(c,55529)),Promise.resolve().then(c.bind(c,2643)),Promise.resolve().then(c.bind(c,25219)),Promise.resolve().then(c.bind(c,53807)),Promise.resolve().then(c.bind(c,82367)),Promise.resolve().then(c.bind(c,38078)),Promise.resolve().then(c.bind(c,85896))},61589:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65904:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx","default")},82367:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(51743),f=c(2643),g=c(4780);function h({title:a,subtitle:b,description:c,primaryCTA:h,secondaryCTA:i,backgroundImage:j}){return(0,d.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[j&&(0,d.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20",style:{backgroundImage:`url(${j})`}}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-background/90 to-surface/50"}),(0,d.jsxs)(e.P.div,{className:"relative z-10 container mx-auto px-4 text-center",variants:g.bK,initial:"hidden",animate:"visible",children:[b&&(0,d.jsx)(e.P.p,{variants:g.HM,className:"text-accent-primary font-medium uppercase tracking-wider mb-4",children:b}),(0,d.jsx)(e.P.h1,{variants:g.HM,className:"font-heading text-h1 font-bold text-text-primary mb-6 leading-tight",children:a}),c&&(0,d.jsx)(e.P.p,{variants:g.HM,className:"text-xl text-text-secondary max-w-2xl mx-auto mb-8 leading-relaxed",children:c}),(h||i)&&(0,d.jsxs)(e.P.div,{variants:g.HM,className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[h&&(0,d.jsx)(f.default,{variant:"primary",size:"lg",href:h.href,children:h.text}),i&&(0,d.jsx)(f.default,{variant:"secondary",size:"lg",href:i.href,children:i.text})]})]}),(0,d.jsxs)("div",{className:"absolute inset-0 pointer-events-none",children:[(0,d.jsx)(e.P.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-accent-primary rounded-full",animate:{y:[0,-20,0],opacity:[.3,1,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut"}}),(0,d.jsx)(e.P.div,{className:"absolute top-1/3 right-1/3 w-1 h-1 bg-accent-secondary rounded-full",animate:{y:[0,-15,0],opacity:[.5,1,.5]},transition:{duration:2.5,repeat:1/0,ease:"easeInOut",delay:1}}),(0,d.jsx)(e.P.div,{className:"absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-accent-primary rounded-full",animate:{y:[0,-10,0],opacity:[.4,1,.4]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.5}})]})]})}},83853:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx","default")},85896:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var g=c(4780);function h({testimonial:a}){return(0,d.jsxs)(e.P.div,{variants:g.HM,className:"bg-surface border border-border p-6 h-full flex flex-col",children:[(0,d.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((b,c)=>(0,d.jsx)(f,{size:16,className:`${c<a.rating?"text-accent-primary fill-current":"text-border"}`},c))}),(0,d.jsxs)("blockquote",{className:"text-text-primary mb-6 flex-grow",children:["“",a.content,"”"]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("cite",{className:"font-medium text-text-primary not-italic",children:a.name}),a.location&&(0,d.jsx)("p",{className:"text-text-secondary text-sm",children:a.location})]}),a.platform&&(0,d.jsxs)("div",{className:"text-accent-primary text-sm font-medium",children:["via ",a.platform]})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24,425,474,389],()=>b(b.s=35404));module.exports=c})();