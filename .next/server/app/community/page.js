(()=>{var a={};a.id=28,a.ids=[28],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2160:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["community",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,76503)),"/Users/<USER>/Documents/personal/femmepod-new/src/app/community/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/personal/femmepod-new/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/personal/femmepod-new/src/app/community/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/community/page",pathname:"/community",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/community/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14143:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/layout/Layout.tsx","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25046:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/TestimonialCard.tsx","default")},26373:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(61120);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41382:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},49972:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},53807:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(51743),f=c(4780);function g({children:a,columns:b={sm:1,md:2,lg:3,xl:3},gap:c=6,className:g="",animate:h=!0}){let i=`
    grid
    grid-cols-${b.sm||1}
    md:grid-cols-${b.md||2}
    lg:grid-cols-${b.lg||3}
    xl:grid-cols-${b.xl||3}
    gap-${c}
    ${g}
  `;return h?(0,d.jsx)(e.P.div,{className:i,variants:f.bK,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-50px"},children:a}):(0,d.jsx)("div",{className:i,children:a})}},61589:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Grid.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65904:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Section.tsx","default")},73122:(a,b,c)=>{Promise.resolve().then(c.bind(c,14143)),Promise.resolve().then(c.bind(c,83853)),Promise.resolve().then(c.bind(c,61589)),Promise.resolve().then(c.bind(c,65904)),Promise.resolve().then(c.bind(c,25046))},76503:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,metadata:()=>r});var d=c(37413),e=c(14143),f=c(65904),g=c(61589),h=c(25046),i=c(83853),j=c(67939),k=c(41382),l=c(49972),m=c(85838),n=c(26373);let o=(0,n.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),p=(0,n.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),q=(0,n.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),r={title:"Community | Femmepod",description:"Join the Femmepod community! See testimonials, customer stories, and connect with fellow art lovers who share our passion for empowering design.",keywords:["community","testimonials","customer stories","art community","Femmepod fans"]};function s(){let a=(0,j.ow)(),b=(0,j.Q2)();return(0,d.jsxs)(e.default,{children:[(0,d.jsx)(f.default,{title:"Join the Femmepod Community",subtitle:"Art Lovers Unite",className:"bg-gradient-to-br from-background to-surface",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,d.jsx)("p",{className:"text-xl text-text-secondary leading-relaxed mb-8",children:"Welcome to a community of art lovers, dreamers, and modern chimeras who believe in the power of empowering design. Here's what our amazing community has to say about their Femmepod experience."}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 text-center",children:[(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(k.A,{size:32,className:"text-background"})}),(0,d.jsx)("div",{className:"text-2xl font-heading font-bold text-text-primary",children:"5K+"}),(0,d.jsx)("div",{className:"text-text-secondary text-sm",children:"Community Members"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(l.A,{size:32,className:"text-background"})}),(0,d.jsx)("div",{className:"text-2xl font-heading font-bold text-text-primary",children:b.stats.fiveStarReviews}),(0,d.jsx)("div",{className:"text-text-secondary text-sm",children:"5-Star Reviews"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(m.A,{size:32,className:"text-background"})}),(0,d.jsx)("div",{className:"text-2xl font-heading font-bold text-text-primary",children:b.stats.customerSatisfaction}),(0,d.jsx)("div",{className:"text-text-secondary text-sm",children:"Satisfaction Rate"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-accent-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(o,{size:32,className:"text-background"})}),(0,d.jsx)("div",{className:"text-2xl font-heading font-bold text-text-primary",children:b.stats.countriesShipped}),(0,d.jsx)("div",{className:"text-text-secondary text-sm",children:"Countries Reached"})]})]})]})}),(0,d.jsx)(f.default,{title:"What Our Community Says",subtitle:"Real Stories, Real Love",children:(0,d.jsx)(g.default,{columns:{sm:1,md:2,lg:3},children:a.map(a=>(0,d.jsx)(h.default,{testimonial:a},a.id))})}),(0,d.jsxs)(f.default,{title:"Community Showcase",subtitle:"Your Femmepod Style",className:"bg-surface",children:[(0,d.jsx)("div",{className:"text-center mb-12",children:(0,d.jsx)("p",{className:"text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto",children:"See how our community styles their Femmepod designs! Tag us on social media to be featured in our community showcase."})}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[1,2,3,4,5,6,7,8].map(a=>(0,d.jsx)("div",{className:"aspect-square bg-background border border-border flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center text-text-secondary",children:[(0,d.jsx)(p,{size:32,className:"mx-auto mb-2"}),(0,d.jsxs)("div",{className:"text-sm",children:["Community Photo #",a]})]})},a))}),(0,d.jsxs)("div",{className:"text-center mt-8",children:[(0,d.jsx)("p",{className:"text-text-secondary mb-4",children:"Share your Femmepod style and get featured!"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsxs)(i.default,{href:b.links.instagram,variant:"primary",size:"lg",external:!0,children:[(0,d.jsx)(p,{size:20,className:"mr-2"}),"Follow on Instagram"]}),(0,d.jsx)(i.default,{href:"/designs",variant:"secondary",size:"lg",children:"Shop the Collection"})]})]})]}),(0,d.jsx)(f.default,{title:"Our Community Values",subtitle:"What We Stand For",children:(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(m.A,{size:40,className:"text-background"})}),(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-4",children:"Empowerment Through Art"}),(0,d.jsx)("p",{className:"text-text-secondary",children:"We believe art has the power to inspire, empower, and create positive change. Every design tells a story of strength and resilience."})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(k.A,{size:40,className:"text-background"})}),(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-4",children:"Inclusive Community"}),(0,d.jsx)("p",{className:"text-text-secondary",children:"Our community welcomes everyone who appreciates powerful, meaningful art. We celebrate diversity and support each other's journeys."})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-accent-primary rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(l.A,{size:40,className:"text-background"})}),(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-4",children:"Quality & Authenticity"}),(0,d.jsx)("p",{className:"text-text-secondary",children:"We're committed to creating authentic, high-quality art that resonates with our community and stands the test of time."})]})]})}),(0,d.jsx)(f.default,{title:"Connect With Us",subtitle:"Join the Conversation",className:"bg-gradient-to-br from-surface to-background",children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"bg-background border border-border p-8 text-center",children:[(0,d.jsx)(p,{size:48,className:"text-accent-primary mx-auto mb-4"}),(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-4",children:"Follow on Instagram"}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Get behind-the-scenes content, work-in-progress shots, and connect with the community daily."}),(0,d.jsx)(i.default,{href:b.links.instagram,variant:"primary",external:!0,children:"@femmepod"})]}),(0,d.jsxs)("div",{className:"bg-background border border-border p-8 text-center",children:[(0,d.jsx)(q,{size:48,className:"text-accent-primary mx-auto mb-4"}),(0,d.jsx)("h3",{className:"font-heading text-xl font-semibold text-text-primary mb-4",children:"Get in Touch"}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Have questions, feedback, or just want to say hello? We'd love to hear from you!"}),(0,d.jsx)(i.default,{href:`mailto:${b.links.email}`,variant:"secondary",children:"Send Email"})]})]}),(0,d.jsxs)("div",{className:"text-center mt-12",children:[(0,d.jsx)("h3",{className:"font-heading text-2xl font-semibold text-text-primary mb-4",children:"Ready to Join the Community?"}),(0,d.jsx)("p",{className:"text-xl text-text-secondary mb-8 leading-relaxed",children:"Start your journey with a design that speaks to your soul, or commission something uniquely yours."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(i.default,{href:"/designs",variant:"primary",size:"lg",children:"Browse Designs"}),(0,d.jsx)(i.default,{href:"/custom-commissions",variant:"secondary",size:"lg",children:"Commission Art"})]})]})]})})]})}},83853:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/personal/femmepod-new/src/components/ui/Button.tsx","default")},85838:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},85896:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var g=c(4780);function h({testimonial:a}){return(0,d.jsxs)(e.P.div,{variants:g.HM,className:"bg-surface border border-border p-6 h-full flex flex-col",children:[(0,d.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((b,c)=>(0,d.jsx)(f,{size:16,className:`${c<a.rating?"text-accent-primary fill-current":"text-border"}`},c))}),(0,d.jsxs)("blockquote",{className:"text-text-primary mb-6 flex-grow",children:["“",a.content,"”"]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("cite",{className:"font-medium text-text-primary not-italic",children:a.name}),a.location&&(0,d.jsx)("p",{className:"text-text-secondary text-sm",children:a.location})]}),a.platform&&(0,d.jsxs)("div",{className:"text-accent-primary text-sm font-medium",children:["via ",a.platform]})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91274:(a,b,c)=>{Promise.resolve().then(c.bind(c,55529)),Promise.resolve().then(c.bind(c,2643)),Promise.resolve().then(c.bind(c,53807)),Promise.resolve().then(c.bind(c,38078)),Promise.resolve().then(c.bind(c,85896))}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24,425,389],()=>b(b.s=2160));module.exports=c})();